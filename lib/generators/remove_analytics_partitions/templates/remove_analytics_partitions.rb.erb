class <%= migration_class_name %> < ActiveRecord::Migration[8.0]
  def up
    safety_assured do
      execute <<~SQL
        ALTER TABLE analytics_events DETACH PARTITION analytics_events_<%= partition_date %>;
        ALTER TABLE analytics_shipper_events DETACH PARTITION analytics_shipper_events_<%= partition_date %>;
        ALTER TABLE analytics_visits DETACH PARTITION analytics_visits_<%= partition_date %>;

        DROP TABLE analytics_events_<%= partition_date %>;
        DROP TABLE analytics_shipper_events_<%= partition_date %>;
        DROP TABLE analytics_visits_<%= partition_date %>;
      SQL
    end
  end

  def down
    safety_assured do
      Analytics::PartitionByMonth.premake(table: 'analytics_events', from: Date.new(<%= year %>, <%= month %>, 1), months: 1)
      Analytics::PartitionByMonth.premake(table: 'analytics_shipper_events', from: Date.new(<%= year %>, <%= month %>, 1), months: 1)
      Analytics::PartitionByMonth.premake(table: 'analytics_visits', from: Date.new(<%= year %>, <%= month %>, 1), months: 1)
    end
  end
end
