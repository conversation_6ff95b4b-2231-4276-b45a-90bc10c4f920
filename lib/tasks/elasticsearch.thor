class ElasticsearchTasks < Thor
  namespace :elasticsearch

  desc 'load_analytics', 'Load analytics data into Elasticsearch'
  method_option :start, type: :numeric
  method_option :finish, type: :numeric
  method_option :date, type: :string, desc: 'Date in the format YYYY-MM-DD. Defaults to today.'
  method_option :index, type: :string
  method_option :time_range, type: :string, desc: 'Time range in the format YYYY-MM-DD..YYYY-MM-DD'
  def load_analytics
    Elastic::PutIndexTemplate.call(Analytics::ShipperEvent)

    time_range = if options[:time_range].present?
                   start_date, end_date = options[:time_range].split('..')
                   Date.parse(start_date).beginning_of_day..Date.parse(end_date).end_of_day
                 else
                   Date.parse(options.fetch(:date, Time.zone.today.to_s)).to_time.utc.all_month
                 end

    Elastic::Analytics::ShipperEvents::BulkImport.call(
      index: options.fetch(
        :index, [Analytics::ShipperEvent.index_name, Time.zone.now.strftime('%Y%m%d%H%M%S')].join('_')
      ),
      start: options[:start],
      finish: options[:finish],
      time_range:
    )
  end

  desc 'put_index_templates', 'Put index templates for all models'
  def put_index_templates
    Rails.autoloaders.main.eager_load

    # rubocop:disable Rails/FindEach
    Elasticsearch::Model::Registry.all.each do |model|
      Elastic::PutIndexTemplate.call(model)
    end
    # rubocop:enable Rails/FindEach
  end

  desc 'reindex', 'Reindex model'
  method_option :model, type: :string, required: true
  method_option :suffix, type: :string
  def reindex
    Elastic::ReindexJob.load(options[:model], suffix: options.fetch(:suffix, Time.zone.now.strftime('%Y%m%d%H%M%S')))
  end
end
