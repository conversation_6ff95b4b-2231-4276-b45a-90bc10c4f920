class AnalyticsTasks < Thor
  namespace :analytics

  desc 'match_prospective_companies', 'Match prospective companies against existing records'
  method_option :bucket, type: :string, default: 'carriersource-v2-files-production', required: true
  method_option :input, type: :string, default: 'manual-uploads/prospective-companies.csv', required: true
  method_option :output, type: :string, default: 'manual-uploads/prospective-companies-matched.csv', required: true
  method_option :field_mappings, type: :hash, default: {}, required: true
  def match_prospective_companies
    Analytics::MatchProspectiveCompanies.call(
      bucket: options[:bucket],
      input: options[:input],
      output: options[:output],
      field_mappings: options[:field_mappings]
    )
  end
end
