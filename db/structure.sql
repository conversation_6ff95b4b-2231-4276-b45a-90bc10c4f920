\restrict unJ3wbqUpGLOTnIGkUQcMFggVg233fwDWjh7SxZgp60QrYRnSc5ZhrddOZYUPsC

-- Dumped from database version 17.4 (Debian 17.4-1.pgdg110+2)
-- Dumped by pg_dump version 17.6 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: citext; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS citext WITH SCHEMA public;


--
-- Name: EXTENSION citext; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION citext IS 'data type for case-insensitive character strings';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_stat_statements IS 'track execution statistics of all SQL statements executed';


--
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- Name: postgis; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS postgis WITH SCHEMA public;


--
-- Name: EXTENSION postgis; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION postgis IS 'PostGIS geometry, geography, and raster spatial types and functions';


--
-- Name: postgis_raster; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS postgis_raster WITH SCHEMA public;


--
-- Name: EXTENSION postgis_raster; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION postgis_raster IS 'PostGIS raster types and functions';


--
-- Name: tablefunc; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS tablefunc WITH SCHEMA public;


--
-- Name: EXTENSION tablefunc; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION tablefunc IS 'functions that manipulate whole tables, including crosstab';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: entity_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.entity_type AS ENUM (
    'carrier',
    'shipper',
    'broker',
    'registrant',
    'freight_forwarder',
    'cargo_tank',
    'iep'
);


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: access_package_allotments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.access_package_allotments (
    id bigint NOT NULL,
    access_package_id bigint NOT NULL,
    name character varying NOT NULL,
    used integer DEFAULT 0 NOT NULL,
    allotment numeric NOT NULL,
    period daterange NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: access_package_allotments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.access_package_allotments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: access_package_allotments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.access_package_allotments_id_seq OWNED BY public.access_package_allotments.id;


--
-- Name: access_packages; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.access_packages (
    id bigint NOT NULL,
    resource_type character varying NOT NULL,
    resource_id bigint NOT NULL,
    active boolean DEFAULT false NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    features character varying[] DEFAULT '{}'::character varying[] NOT NULL,
    subscription_id bigint,
    packages character varying[] DEFAULT '{}'::character varying[] NOT NULL
);


--
-- Name: access_packages_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.access_packages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: access_packages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.access_packages_id_seq OWNED BY public.access_packages.id;


--
-- Name: action_text_rich_texts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.action_text_rich_texts (
    id bigint NOT NULL,
    name character varying NOT NULL,
    body text,
    record_type character varying NOT NULL,
    record_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: action_text_rich_texts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.action_text_rich_texts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: action_text_rich_texts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.action_text_rich_texts_id_seq OWNED BY public.action_text_rich_texts.id;


--
-- Name: active_admin_comments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.active_admin_comments (
    id bigint NOT NULL,
    namespace character varying,
    body text,
    resource_type character varying,
    resource_id bigint,
    author_type character varying,
    author_id bigint,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: active_admin_comments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.active_admin_comments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: active_admin_comments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.active_admin_comments_id_seq OWNED BY public.active_admin_comments.id;


--
-- Name: active_campaign_records; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.active_campaign_records (
    id bigint NOT NULL,
    record_type character varying NOT NULL,
    record_id bigint NOT NULL,
    external_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: active_campaign_records_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.active_campaign_records_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: active_campaign_records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.active_campaign_records_id_seq OWNED BY public.active_campaign_records.id;


--
-- Name: active_storage_attachments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.active_storage_attachments (
    id bigint NOT NULL,
    name character varying NOT NULL,
    record_type character varying NOT NULL,
    record_id bigint NOT NULL,
    blob_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: active_storage_attachments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.active_storage_attachments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: active_storage_attachments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.active_storage_attachments_id_seq OWNED BY public.active_storage_attachments.id;


--
-- Name: active_storage_blobs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.active_storage_blobs (
    id bigint NOT NULL,
    key character varying NOT NULL,
    filename character varying NOT NULL,
    content_type character varying,
    metadata text,
    service_name character varying NOT NULL,
    byte_size bigint NOT NULL,
    checksum character varying,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: active_storage_blobs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.active_storage_blobs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: active_storage_blobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.active_storage_blobs_id_seq OWNED BY public.active_storage_blobs.id;


--
-- Name: active_storage_variant_records; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.active_storage_variant_records (
    id bigint NOT NULL,
    blob_id bigint NOT NULL,
    variation_digest character varying NOT NULL
);


--
-- Name: active_storage_variant_records_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.active_storage_variant_records_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: active_storage_variant_records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.active_storage_variant_records_id_seq OWNED BY public.active_storage_variant_records.id;


--
-- Name: analytics_aggregate_shipper_events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events (
    id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
)
PARTITION BY RANGE (interval_date);


--
-- Name: analytics_aggregate_shipper_events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_aggregate_shipper_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_aggregate_shipper_events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_aggregate_shipper_events_id_seq OWNED BY public.analytics_aggregate_shipper_events.id;


--
-- Name: analytics_aggregate_shipper_events_2025_02; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_02 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_03; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_03 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_04; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_04 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_05; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_05 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_06; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_06 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_07; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_07 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_08; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_08 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_09; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_09 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_aggregate_shipper_events_2025_10; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_aggregate_shipper_events_2025_10 (
    id bigint DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass) NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "interval" character varying,
    interval_date date,
    event_count integer,
    visit_id bigint,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_companies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_companies (
    id bigint NOT NULL,
    name character varying NOT NULL,
    domain character varying,
    twitter character varying,
    facebook character varying,
    linkedin character varying,
    industry_id bigint,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    revenue_year integer,
    revenue_amount numeric(17,2),
    city_id bigint,
    short_name character varying,
    uuid uuid DEFAULT public.uuid_generate_v4(),
    phone character varying,
    employee_range character varying
);


--
-- Name: analytics_companies_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_companies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_companies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_companies_id_seq OWNED BY public.analytics_companies.id;


--
-- Name: analytics_company_event_feed_exports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_company_event_feed_exports (
    id bigint NOT NULL,
    feed_id bigint NOT NULL,
    user_id bigint NOT NULL,
    email public.citext NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_company_event_feed_exports_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_company_event_feed_exports_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_company_event_feed_exports_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_company_event_feed_exports_id_seq OWNED BY public.analytics_company_event_feed_exports.id;


--
-- Name: analytics_company_event_feed_notification_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_company_event_feed_notification_logs (
    id bigint NOT NULL,
    notification_id bigint NOT NULL,
    response jsonb,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    analytics_company_id bigint,
    payload jsonb,
    synced boolean DEFAULT true NOT NULL
);


--
-- Name: analytics_company_event_feed_notification_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_company_event_feed_notification_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_company_event_feed_notification_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_company_event_feed_notification_logs_id_seq OWNED BY public.analytics_company_event_feed_notification_logs.id;


--
-- Name: analytics_company_event_feed_notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_company_event_feed_notifications (
    id bigint NOT NULL,
    feed_id bigint NOT NULL,
    notification_type character varying NOT NULL,
    properties jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    integration_id bigint,
    "interval" character varying DEFAULT 'immediate'::character varying NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4()
);


--
-- Name: analytics_company_event_feed_notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_company_event_feed_notifications_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_company_event_feed_notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_company_event_feed_notifications_id_seq OWNED BY public.analytics_company_event_feed_notifications.id;


--
-- Name: analytics_company_event_feeds; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_company_event_feeds (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    name character varying NOT NULL,
    filters jsonb DEFAULT '{}'::jsonb NOT NULL,
    editable boolean DEFAULT true NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    entity_type character varying NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4()
);


--
-- Name: analytics_company_event_feeds_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_company_event_feeds_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_company_event_feeds_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_company_event_feeds_id_seq OWNED BY public.analytics_company_event_feeds.id;


--
-- Name: analytics_company_providers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_company_providers (
    id bigint NOT NULL,
    name character varying,
    domain character varying NOT NULL,
    provider character varying NOT NULL,
    twitter character varying,
    facebook character varying,
    linkedin character varying,
    revenue_amount numeric(17,2),
    industry_id bigint,
    payload jsonb DEFAULT '{}'::jsonb,
    city_id bigint,
    short_name character varying,
    phone character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    employee_range character varying
);


--
-- Name: analytics_company_providers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_company_providers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_company_providers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_company_providers_id_seq OWNED BY public.analytics_company_providers.id;


--
-- Name: analytics_event_feeds; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_event_feeds (
    id bigint NOT NULL,
    name character varying NOT NULL,
    filters jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_event_feeds_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_event_feeds_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_event_feeds_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_event_feeds_id_seq OWNED BY public.analytics_event_feeds.id;


--
-- Name: analytics_events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events (
    id bigint NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
)
PARTITION BY RANGE ("time");


--
-- Name: analytics_events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_events_id_seq OWNED BY public.analytics_events.id;


--
-- Name: analytics_events_2024_09; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2024_09 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2024_10; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2024_10 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2024_11; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2024_11 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2024_12; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2024_12 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_01; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_01 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_02; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_02 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_03; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_03 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_04; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_04 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_05; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_05 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_06; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_06 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_07; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_07 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_08; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_08 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_09; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_09 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_events_2025_10; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_events_2025_10 (
    id bigint DEFAULT nextval('public.analytics_events_id_seq'::regclass) NOT NULL,
    visit_id bigint NOT NULL,
    user_id bigint,
    name character varying NOT NULL,
    properties jsonb,
    "time" timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_industries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_industries (
    id bigint NOT NULL,
    name character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_industries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_industries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_industries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_industries_id_seq OWNED BY public.analytics_industries.id;


--
-- Name: analytics_integrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_integrations (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    entity_type character varying NOT NULL,
    provider character varying NOT NULL,
    settings jsonb DEFAULT '{}'::jsonb,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_integrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_integrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_integrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_integrations_id_seq OWNED BY public.analytics_integrations.id;


--
-- Name: analytics_load_industries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_load_industries (
    id bigint NOT NULL,
    industry_id bigint NOT NULL,
    load_gid character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: analytics_load_industries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_load_industries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_load_industries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_load_industries_id_seq OWNED BY public.analytics_load_industries.id;


--
-- Name: analytics_shipper_events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events (
    id bigint NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
)
PARTITION BY RANGE ("time");


--
-- Name: analytics_shipper_events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_shipper_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_shipper_events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_shipper_events_id_seq OWNED BY public.analytics_shipper_events.id;


--
-- Name: analytics_shipper_events_2024_09; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2024_09 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2024_10; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2024_10 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2024_11; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2024_11 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2024_12; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2024_12 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_01; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_01 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_02; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_02 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_03; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_03 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_04; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_04 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_05; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_05 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_06; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_06 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_07; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_07 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_08; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_08 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_09; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_09 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_shipper_events_2025_10; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_shipper_events_2025_10 (
    id bigint DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass) NOT NULL,
    event_id bigint NOT NULL,
    visit_id bigint NOT NULL,
    analytics_company_id bigint NOT NULL,
    type character varying NOT NULL,
    url character varying,
    company_id bigint,
    company_entity_type character varying,
    search_city_id bigint,
    search_state_id character varying,
    search_region_id character varying,
    search_country_id character varying,
    search_freight_ids bigint[],
    search_truck_type_ids bigint[],
    search_shipment_type_ids bigint[],
    search_specialized_service_ids bigint[],
    "time" timestamp(6) without time zone NOT NULL,
    search_destination_city_id bigint,
    search_destination_state_id character varying,
    search_destination_region_id character varying,
    search_destination_country_id character varying
);


--
-- Name: analytics_visits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits (
    id bigint NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
)
PARTITION BY RANGE (started_at);


--
-- Name: analytics_visits_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.analytics_visits_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: analytics_visits_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.analytics_visits_id_seq OWNED BY public.analytics_visits.id;


--
-- Name: analytics_visits_2024_09; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2024_09 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2024_10; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2024_10 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2024_11; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2024_11 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2024_12; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2024_12 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_01; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_01 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_02; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_02 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_03; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_03 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_04; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_04 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_05; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_05 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_06; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_06 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_07; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_07 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_08; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_08 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_09; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_09 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: analytics_visits_2025_10; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.analytics_visits_2025_10 (
    id bigint DEFAULT nextval('public.analytics_visits_id_seq'::regclass) NOT NULL,
    visit_token character varying NOT NULL,
    visitor_token character varying NOT NULL,
    user_id bigint,
    ip inet NOT NULL,
    user_agent text,
    referrer text,
    referring_domain character varying,
    landing_page text,
    browser character varying,
    os character varying,
    device_type character varying,
    started_at timestamp(6) without time zone NOT NULL,
    country character varying,
    region character varying,
    city character varying,
    latitude double precision,
    longitude double precision,
    company_id bigint,
    company_status character varying DEFAULT 'pending'::character varying NOT NULL
);


--
-- Name: api_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.api_tokens (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    value character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: api_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.api_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: api_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.api_tokens_id_seq OWNED BY public.api_tokens.id;


--
-- Name: ar_internal_metadata; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ar_internal_metadata (
    key character varying NOT NULL,
    value character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: audits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.audits (
    id bigint NOT NULL,
    auditable_type character varying,
    auditable_id bigint,
    associated_type character varying,
    associated_id bigint,
    user_type character varying,
    user_id bigint,
    username character varying,
    action character varying,
    audited_changes jsonb,
    version integer DEFAULT 0,
    comment character varying,
    remote_address character varying,
    request_uuid character varying,
    created_at timestamp(6) without time zone
);


--
-- Name: audits_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.audits_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: audits_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.audits_id_seq OWNED BY public.audits.id;


--
-- Name: authentications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.authentications (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    email character varying NOT NULL,
    provider character varying NOT NULL,
    uid character varying NOT NULL,
    payload jsonb DEFAULT '{}'::jsonb,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: authentications_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.authentications_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: authentications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.authentications_id_seq OWNED BY public.authentications.id;


--
-- Name: authhist; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.authhist (
    docket_number character varying(255) NOT NULL,
    common date,
    contract date,
    broker date,
    id bigint NOT NULL
);


--
-- Name: authhist_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.authhist ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.authhist_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: basics_measure; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.basics_measure (
    dot_number integer NOT NULL,
    unsafe_driv_measure real,
    unsafe_driv_percentile real,
    hos_driv_measure real,
    hos_driv_percentile real,
    driv_fit_measure real,
    driv_fit_percentile real,
    contr_subst_measure real,
    contr_subst_percentile real,
    veh_maint_measure real,
    veh_maint_percentile real
);


--
-- Name: blocked_ips; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.blocked_ips (
    id bigint NOT NULL,
    ip inet NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: blocked_ips_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.blocked_ips_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: blocked_ips_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.blocked_ips_id_seq OWNED BY public.blocked_ips.id;


--
-- Name: bookmarks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.bookmarks (
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    company_id bigint NOT NULL,
    id bigint NOT NULL,
    list_id bigint NOT NULL,
    entity_type character varying NOT NULL
);


--
-- Name: bookmarks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.bookmarks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: bookmarks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.bookmarks_id_seq OWNED BY public.bookmarks.id;


--
-- Name: brokerage_cities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_cities (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_cities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_cities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_cities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_cities_id_seq OWNED BY public.brokerage_cities.id;


--
-- Name: brokerage_domains; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_domains (
    email_domain public.citext NOT NULL,
    company_id bigint NOT NULL,
    id bigint NOT NULL
);


--
-- Name: brokerage_domains_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_domains_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_domains_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_domains_id_seq OWNED BY public.brokerage_domains.id;


--
-- Name: brokerage_onboardings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_onboardings (
    id bigint NOT NULL,
    brokerage_profile_id bigint NOT NULL,
    onboarding_system character varying NOT NULL,
    onboarding_link character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_onboardings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_onboardings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_onboardings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_onboardings_id_seq OWNED BY public.brokerage_onboardings.id;


--
-- Name: brokerage_profile_assets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_profile_assets (
    id bigint NOT NULL,
    brokerage_profile_id bigint NOT NULL,
    title character varying,
    description text,
    asset_type character varying NOT NULL,
    row_order integer,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_profile_assets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_profile_assets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_profile_assets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_profile_assets_id_seq OWNED BY public.brokerage_profile_assets.id;


--
-- Name: brokerage_profile_contacts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_profile_contacts (
    id bigint NOT NULL,
    brokerage_profile_id bigint NOT NULL,
    email public.citext,
    name character varying,
    phone character varying,
    type character varying NOT NULL,
    "default" boolean DEFAULT false NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_profile_contacts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_profile_contacts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_profile_contacts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_profile_contacts_id_seq OWNED BY public.brokerage_profile_contacts.id;


--
-- Name: brokerage_profile_preferred_lanes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_profile_preferred_lanes (
    id bigint NOT NULL,
    brokerage_profile_id bigint NOT NULL,
    pickup_city_id bigint NOT NULL,
    dropoff_city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_profile_preferred_lanes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_profile_preferred_lanes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_profile_preferred_lanes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_profile_preferred_lanes_id_seq OWNED BY public.brokerage_profile_preferred_lanes.id;


--
-- Name: brokerage_profile_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_profile_users (
    id bigint NOT NULL,
    brokerage_profile_id bigint NOT NULL,
    user_id bigint NOT NULL,
    status character varying,
    verification_token character varying,
    verified_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_profile_users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_profile_users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_profile_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_profile_users_id_seq OWNED BY public.brokerage_profile_users.id;


--
-- Name: brokerage_profile_widgets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_profile_widgets (
    id bigint NOT NULL,
    brokerage_profile_id bigint NOT NULL,
    widget_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_profile_widgets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_profile_widgets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_profile_widgets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_profile_widgets_id_seq OWNED BY public.brokerage_profile_widgets.id;


--
-- Name: brokerage_profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_profiles (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    bio text,
    specialized_services bigint[] DEFAULT '{}'::bigint[],
    no_specialized_services boolean DEFAULT false NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    truck_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    shipment_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    website_url character varying,
    cs_score double precision,
    hide_street boolean DEFAULT false NOT NULL
);


--
-- Name: brokerage_profiles_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_profiles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_profiles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_profiles_id_seq OWNED BY public.brokerage_profiles.id;


--
-- Name: brokerage_review_aggregates; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_review_aggregates (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    review_count integer,
    star_rating numeric(3,2),
    nps numeric(4,2),
    communication numeric(4,2),
    sentiments jsonb,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_review_aggregates_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_review_aggregates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_review_aggregates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_review_aggregates_id_seq OWNED BY public.brokerage_review_aggregates.id;


--
-- Name: brokerage_review_replies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_review_replies (
    id bigint NOT NULL,
    review_id bigint NOT NULL,
    user_id bigint NOT NULL,
    comment text NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_review_replies_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_review_replies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_review_replies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_review_replies_id_seq OWNED BY public.brokerage_review_replies.id;


--
-- Name: brokerage_review_reports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_review_reports (
    id bigint NOT NULL,
    review_id bigint NOT NULL,
    user_id bigint NOT NULL,
    reason character varying NOT NULL,
    description text NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_review_reports_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_review_reports_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_review_reports_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_review_reports_id_seq OWNED BY public.brokerage_review_reports.id;


--
-- Name: brokerage_review_sentiments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_review_sentiments (
    id bigint NOT NULL,
    review_id bigint NOT NULL,
    sentiment_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_review_sentiments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_review_sentiments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_review_sentiments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_review_sentiments_id_seq OWNED BY public.brokerage_review_sentiments.id;


--
-- Name: brokerage_reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_reviews (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    user_id bigint NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    rejection_reason character varying,
    title character varying,
    nps integer,
    star_rating numeric(3,2),
    persona_id bigint,
    industry_id bigint,
    utm_param_id bigint,
    is_consider_next_time boolean,
    body text,
    last_worked_with date,
    discovery character varying,
    consider_expensive integer,
    how_often integer,
    communication integer,
    freights integer[] DEFAULT '{}'::integer[] NOT NULL,
    specialized_services bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    electronic_tracking boolean,
    offer_reference boolean,
    related boolean,
    anonymous boolean,
    submitted_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    truck_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    shipment_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    featured boolean DEFAULT false NOT NULL
);


--
-- Name: brokerage_reviews_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_reviews_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_reviews_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_reviews_id_seq OWNED BY public.brokerage_reviews.id;


--
-- Name: brokerage_reviews_review_lanes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerage_reviews_review_lanes (
    id bigint NOT NULL,
    review_id bigint NOT NULL,
    pickup_city_id bigint NOT NULL,
    dropoff_city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerage_reviews_review_lanes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerage_reviews_review_lanes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerage_reviews_review_lanes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerage_reviews_review_lanes_id_seq OWNED BY public.brokerage_reviews_review_lanes.id;


--
-- Name: brokerages_review_lanes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerages_review_lanes (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    pickup_city_id bigint NOT NULL,
    dropoff_city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerages_review_lanes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerages_review_lanes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerages_review_lanes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerages_review_lanes_id_seq OWNED BY public.brokerages_review_lanes.id;


--
-- Name: brokerages_shipment_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerages_shipment_types (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    shipment_type_id bigint NOT NULL
);


--
-- Name: brokerages_shipment_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerages_shipment_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerages_shipment_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerages_shipment_types_id_seq OWNED BY public.brokerages_shipment_types.id;


--
-- Name: brokerages_specialized_services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerages_specialized_services (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    specialized_service_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: brokerages_specialized_services_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerages_specialized_services_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerages_specialized_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerages_specialized_services_id_seq OWNED BY public.brokerages_specialized_services.id;


--
-- Name: brokerages_truck_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.brokerages_truck_types (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    truck_type_id bigint NOT NULL
);


--
-- Name: brokerages_truck_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.brokerages_truck_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brokerages_truck_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.brokerages_truck_types_id_seq OWNED BY public.brokerages_truck_types.id;


--
-- Name: carrier_availabilities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_availabilities (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    date date NOT NULL,
    length integer,
    weight integer,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    origin_type character varying,
    origin_region_ids character varying[] DEFAULT '{}'::character varying[],
    origin_state_ids character varying[] DEFAULT '{}'::character varying[],
    destination_type character varying,
    destination_region_ids character varying[] DEFAULT '{}'::character varying[],
    destination_state_ids character varying[] DEFAULT '{}'::character varying[],
    truck_type_id bigint NOT NULL,
    shipment_type_id bigint,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL
);


--
-- Name: carrier_availabilities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_availabilities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_availabilities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_availabilities_id_seq OWNED BY public.carrier_availabilities.id;


--
-- Name: carrier_availability_destinations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_availability_destinations (
    id bigint NOT NULL,
    carrier_availability_id bigint NOT NULL,
    city_id bigint NOT NULL
);


--
-- Name: carrier_availability_destinations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_availability_destinations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_availability_destinations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_availability_destinations_id_seq OWNED BY public.carrier_availability_destinations.id;


--
-- Name: carrier_availability_origins; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_availability_origins (
    id bigint NOT NULL,
    carrier_availability_id bigint NOT NULL,
    city_id bigint NOT NULL
);


--
-- Name: carrier_availability_origins_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_availability_origins_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_availability_origins_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_availability_origins_id_seq OWNED BY public.carrier_availability_origins.id;


--
-- Name: carrier_availability_unsubscribes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_availability_unsubscribes (
    id bigint NOT NULL,
    email public.citext NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_availability_unsubscribes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_availability_unsubscribes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_availability_unsubscribes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_availability_unsubscribes_id_seq OWNED BY public.carrier_availability_unsubscribes.id;


--
-- Name: carrier_cities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_cities (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_cities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_cities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_cities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_cities_id_seq OWNED BY public.carrier_cities.id;


--
-- Name: carrier_csv_exports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_csv_exports (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    rows integer DEFAULT 0 NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_csv_exports_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_csv_exports_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_csv_exports_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_csv_exports_id_seq OWNED BY public.carrier_csv_exports.id;


--
-- Name: carrier_network_builder_lane_entities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_network_builder_lane_entities (
    id bigint NOT NULL,
    carrier_network_builder_lane_id bigint NOT NULL,
    company_id bigint NOT NULL,
    entity_type character varying NOT NULL,
    row_order integer,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_network_builder_lane_entities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_network_builder_lane_entities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_network_builder_lane_entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_network_builder_lane_entities_id_seq OWNED BY public.carrier_network_builder_lane_entities.id;


--
-- Name: carrier_network_builder_lanes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_network_builder_lanes (
    id bigint NOT NULL,
    carrier_network_builder_id bigint NOT NULL,
    refresh_status character varying DEFAULT 'refresh_pending'::character varying NOT NULL,
    refresh_completed_at timestamp(6) without time zone,
    origin_id character varying,
    destination_id character varying,
    volume character varying,
    frequency character varying,
    filters jsonb DEFAULT '{}'::jsonb,
    notes text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    number_of_entities integer
);


--
-- Name: carrier_network_builder_lanes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_network_builder_lanes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_network_builder_lanes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_network_builder_lanes_id_seq OWNED BY public.carrier_network_builder_lanes.id;


--
-- Name: carrier_network_builder_messages; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_network_builder_messages (
    id bigint NOT NULL,
    carrier_network_builder_id bigint NOT NULL,
    role character varying NOT NULL,
    content text NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_network_builder_messages_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_network_builder_messages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_network_builder_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_network_builder_messages_id_seq OWNED BY public.carrier_network_builder_messages.id;


--
-- Name: carrier_network_builders; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_network_builders (
    id bigint NOT NULL,
    user_id bigint,
    name character varying,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_network_builders_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_network_builders_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_network_builders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_network_builders_id_seq OWNED BY public.carrier_network_builders.id;


--
-- Name: carrier_operation_states; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_operation_states (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    state_id character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_operation_states_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_operation_states_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_operation_states_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_operation_states_id_seq OWNED BY public.carrier_operation_states.id;


--
-- Name: carrier_profile_assets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_assets (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    asset_type character varying NOT NULL,
    title character varying,
    description text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    row_order integer
);


--
-- Name: carrier_profile_assets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_assets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_assets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_assets_id_seq OWNED BY public.carrier_profile_assets.id;


--
-- Name: carrier_profile_contacts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_contacts (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    type character varying NOT NULL,
    name character varying,
    email character varying,
    phone character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    "default" boolean DEFAULT false NOT NULL
);


--
-- Name: carrier_profile_contacts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_contacts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_contacts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_contacts_id_seq OWNED BY public.carrier_profile_contacts.id;


--
-- Name: carrier_profile_terminals; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_terminals (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_profile_terminals_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_terminals_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_terminals_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_terminals_id_seq OWNED BY public.carrier_profile_terminals.id;


--
-- Name: carrier_profile_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_users (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    user_id bigint NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    verification_token character varying,
    verified_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_profile_users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_users_id_seq OWNED BY public.carrier_profile_users.id;


--
-- Name: carrier_profile_website_highlights; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_website_highlights (
    id bigint NOT NULL,
    carrier_profile_website_id bigint NOT NULL,
    heading character varying,
    subheading character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_profile_website_highlights_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_website_highlights_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_website_highlights_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_website_highlights_id_seq OWNED BY public.carrier_profile_website_highlights.id;


--
-- Name: carrier_profile_website_reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_website_reviews (
    id bigint NOT NULL,
    carrier_profile_website_id bigint NOT NULL,
    review_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_profile_website_reviews_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_website_reviews_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_website_reviews_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_website_reviews_id_seq OWNED BY public.carrier_profile_website_reviews.id;


--
-- Name: carrier_profile_website_services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_website_services (
    id bigint NOT NULL,
    carrier_profile_website_id bigint NOT NULL,
    name character varying,
    description text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_profile_website_services_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_website_services_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_website_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_website_services_id_seq OWNED BY public.carrier_profile_website_services.id;


--
-- Name: carrier_profile_websites; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_websites (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    domain character varying,
    primary_color character varying,
    heading character varying,
    subheading character varying,
    highlights_heading character varying,
    highlights_subheading character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    street character varying,
    zip character varying,
    hide_street boolean DEFAULT false NOT NULL
);


--
-- Name: carrier_profile_websites_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_websites_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_websites_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_websites_id_seq OWNED BY public.carrier_profile_websites.id;


--
-- Name: carrier_profile_widgets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profile_widgets (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    widget_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carrier_profile_widgets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profile_widgets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profile_widgets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profile_widgets_id_seq OWNED BY public.carrier_profile_widgets.id;


--
-- Name: carrier_profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carrier_profiles (
    id bigint NOT NULL,
    email public.citext,
    bio text,
    street character varying,
    city character varying,
    state character varying,
    zip character varying,
    country character varying,
    phone character varying,
    company_rep1 character varying,
    company_rep2 character varying,
    specialized_services bigint[] DEFAULT '{}'::bigint[],
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    no_additional_terminals boolean DEFAULT false NOT NULL,
    company_id bigint NOT NULL,
    website_url character varying,
    no_specialized_services boolean DEFAULT false NOT NULL,
    driver_application_url character varying,
    facebook character varying,
    twitter character varying,
    linkedin character varying,
    instagram character varying,
    truck_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    shipment_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    cs_score double precision,
    hide_street boolean DEFAULT false NOT NULL
);


--
-- Name: carrier_profiles_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carrier_profiles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carrier_profiles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carrier_profiles_id_seq OWNED BY public.carrier_profiles.id;


--
-- Name: crash; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.crash (
    report_number character varying(255),
    dot_number integer,
    date character varying(255),
    fatalities integer,
    injuries integer,
    tow_away boolean,
    id bigint NOT NULL,
    crash_id bigint
);


--
-- Name: carriers_accidents; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.carriers_accidents AS
 WITH crash_age AS (
         SELECT crash.dot_number,
            crash.report_number,
            crash.fatalities,
            crash.injuries,
            crash.tow_away,
            (((EXTRACT(year FROM age((CURRENT_DATE)::timestamp with time zone, (to_date((crash.date)::text, 'YYYY-MM-DD'::text))::timestamp with time zone)))::integer * 12) + (EXTRACT(month FROM age((CURRENT_DATE)::timestamp with time zone, (to_date((crash.date)::text, 'YYYY-MM-DD'::text))::timestamp with time zone)))::integer) AS age_bucket
           FROM public.crash
        )
 SELECT dot_number,
        CASE
            WHEN (int4range(0, 6) @> age_bucket) THEN int4range(0, 6)
            WHEN (int4range(6, 12) @> age_bucket) THEN int4range(6, 12)
            WHEN (int4range(12, 24) @> age_bucket) THEN int4range(12, 24)
            ELSE '[24,)'::int4range
        END AS bucket,
    count(report_number) AS crashes,
    sum(fatalities) AS fatalities,
    sum(injuries) AS injuries,
    sum(
        CASE tow_away
            WHEN true THEN 1
            ELSE 0
        END) AS tow_aways,
    now() AS updated_at
   FROM crash_age
  GROUP BY dot_number,
        CASE
            WHEN (int4range(0, 6) @> age_bucket) THEN int4range(0, 6)
            WHEN (int4range(6, 12) @> age_bucket) THEN int4range(6, 12)
            WHEN (int4range(12, 24) @> age_bucket) THEN int4range(12, 24)
            ELSE '[24,)'::int4range
        END
  WITH NO DATA;


--
-- Name: carriers_review_lanes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carriers_review_lanes (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    pickup_city_id bigint NOT NULL,
    dropoff_city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carriers_review_lanes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carriers_review_lanes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carriers_review_lanes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carriers_review_lanes_id_seq OWNED BY public.carriers_review_lanes.id;


--
-- Name: violation; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.violation (
    unique_id character varying(255),
    insp_date character varying(255),
    dot_number integer,
    viol_code character varying(255),
    basic_desc character varying(255),
    oos_indicator boolean,
    oos_weight integer,
    severity_weight integer,
    time_weight integer,
    tot_severity_wght integer,
    viol_value integer,
    section_desc character varying(255),
    group_desc character varying(255),
    viol_unit character varying(255),
    id bigint NOT NULL
);


--
-- Name: carriers_safety_violations; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.carriers_safety_violations AS
 SELECT v.dot_number,
    v.basic_desc,
    sum(
        CASE
            WHEN ((v.oos_indicator = false) AND (v.severity_weight < 7)) THEN 1
            ELSE 0
        END) AS regular,
    sum(
        CASE
            WHEN (v.severity_weight >= 7) THEN 1
            ELSE 0
        END) AS severe,
    sum(
        CASE
            WHEN (v.oos_indicator = true) THEN 1
            ELSE 0
        END) AS oos,
    count(v.unique_id) AS total,
        CASE v.basic_desc
            WHEN 'CONTROLLED_SUBSTANCES'::text THEN max(bm.contr_subst_measure)
            WHEN 'DRIVER_FITNESS'::text THEN max(bm.driv_fit_measure)
            WHEN 'HOURS_OF_SERVICE_COMPLIANCE'::text THEN max(bm.hos_driv_measure)
            WHEN 'UNSAFE_DRIVING'::text THEN max(bm.unsafe_driv_measure)
            WHEN 'VEHICLE_MAINTENANCE'::text THEN max(bm.veh_maint_measure)
            ELSE NULL::real
        END AS measure,
        CASE v.basic_desc
            WHEN 'CONTROLLED_SUBSTANCES'::text THEN max(bm.contr_subst_percentile)
            WHEN 'DRIVER_FITNESS'::text THEN max(bm.driv_fit_percentile)
            WHEN 'HOURS_OF_SERVICE_COMPLIANCE'::text THEN max(bm.hos_driv_percentile)
            WHEN 'UNSAFE_DRIVING'::text THEN max(bm.unsafe_driv_percentile)
            WHEN 'VEHICLE_MAINTENANCE'::text THEN max(bm.veh_maint_percentile)
            ELSE NULL::real
        END AS percentile
   FROM (public.violation v
     LEFT JOIN public.basics_measure bm ON ((v.dot_number = bm.dot_number)))
  GROUP BY v.dot_number, v.basic_desc
  WITH NO DATA;


--
-- Name: carriers_shipment_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carriers_shipment_types (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    company_id bigint NOT NULL,
    shipment_type_id bigint NOT NULL
);


--
-- Name: carriers_shipment_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carriers_shipment_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carriers_shipment_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carriers_shipment_types_id_seq OWNED BY public.carriers_shipment_types.id;


--
-- Name: carriers_specialized_services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carriers_specialized_services (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    specialized_service_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: carriers_specialized_services_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carriers_specialized_services_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carriers_specialized_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carriers_specialized_services_id_seq OWNED BY public.carriers_specialized_services.id;


--
-- Name: carriers_truck_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carriers_truck_types (
    id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    company_id bigint NOT NULL,
    truck_type_id bigint NOT NULL
);


--
-- Name: carriers_truck_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.carriers_truck_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: carriers_truck_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.carriers_truck_types_id_seq OWNED BY public.carriers_truck_types.id;


--
-- Name: census; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.census (
    act_stat character varying(255),
    carship character varying(255),
    dot_number integer,
    name character varying(255),
    name_dba character varying(255),
    dbnum character varying(255),
    phy_natn character varying(255),
    reg character varying(255),
    phy_str character varying(255),
    phy_city character varying(255),
    phy_cnty character varying(255),
    phy_st character varying(255),
    phy_zip character varying(255),
    undeliv_phy character varying(255),
    tel_num character varying(255),
    cell_num character varying(255),
    fax_num character varying(255),
    mai_natn character varying(255),
    mai_str character varying(255),
    mai_city character varying(255),
    mai_cnty character varying(255),
    mai_st character varying(255),
    mai_zip character varying(255),
    undeliv_mai character varying(255),
    oic character varying(255),
    terr character varying(255),
    icc_docket_1_prefix character varying(255),
    icc1 character varying(255),
    icc_docket_2_prefix character varying(255),
    icc2 character varying(255),
    icc_docket_3_prefix character varying(255),
    icc3 character varying(255),
    class character varying(255),
    classdef character varying(255),
    crrinter character varying(255),
    crrhmintra character varying(255),
    crrintra character varying(255),
    shpinter character varying(255),
    shpintra character varying(255),
    vehicle_registrant character varying(255),
    org character varying(255),
    genfreight character varying(255),
    household character varying(255),
    metalsheet character varying(255),
    motorveh character varying(255),
    drivetow character varying(255),
    logpole character varying(255),
    bldgmat character varying(255),
    mobilehome character varying(255),
    machlrg character varying(255),
    produce character varying(255),
    liqgas character varying(255),
    intermodal character varying(255),
    passengers character varying(255),
    oilfield character varying(255),
    livestock character varying(255),
    grainfeed character varying(255),
    coalcoke character varying(255),
    meat character varying(255),
    garbage character varying(255),
    usmail character varying(255),
    chem character varying(255),
    drybulk character varying(255),
    coldfood character varying(255),
    beverages character varying(255),
    paperprod character varying(255),
    utility character varying(255),
    farmsupp character varying(255),
    construct character varying(255),
    waterwell character varying(255),
    cargoothr character varying(255),
    othercargo character varying(255),
    hm_ind character varying(255),
    owntruck character varying(255),
    owntract character varying(255),
    owntrail character varying(255),
    owncoach character varying(255),
    ownschool_1_8 character varying(255),
    ownschool_9_15 character varying(255),
    ownschool_16 character varying(255),
    ownbus_16 character varying(255),
    ownvan_1_8 character varying(255),
    ownvan_9_15 character varying(255),
    ownlimo_1_8 character varying(255),
    ownlimo_9_15 character varying(255),
    ownlimo_16 character varying(255),
    trmtruck character varying(255),
    trmtract character varying(255),
    trmtrail character varying(255),
    trmcoach character varying(255),
    trmschool_1_8 character varying(255),
    trmschool_9_15 character varying(255),
    trmschool_16 character varying(255),
    trmbus_16 character varying(255),
    trmvan_1_8 character varying(255),
    trmvan_9_15 character varying(255),
    trmlimo_1_8 character varying(255),
    trmlimo_9_15 character varying(255),
    trmlimo_16 character varying(255),
    trptruck character varying(255),
    trptract character varying(255),
    trptrail character varying(255),
    trpcoach character varying(255),
    trpschool_1_8 character varying(255),
    trpschool_9_15 character varying(255),
    trpschool_16 character varying(255),
    trpbus_16 character varying(255),
    trpvan_1_8 character varying(255),
    trpvan_9_15 character varying(255),
    trplimo_1_8 character varying(255),
    trplimo_9_15 character varying(255),
    trplimo_16 character varying(255),
    tot_trucks character varying(255),
    tot_buses character varying(255),
    tot_pwr integer,
    fleetsize character varying(255),
    interlt100 character varying(255),
    intergt100 character varying(255),
    inter_drs character varying(255),
    intralt100 character varying(255),
    intragt100 character varying(255),
    intra_drs character varying(255),
    avg_tld character varying(255),
    tot_drs character varying(255),
    cdl_drs character varying(255),
    revtype character varying(255),
    revdocnum character varying(255),
    revdate character varying(255),
    acc_rate character varying(255),
    repprevrat character varying(255),
    mlg150 character varying(255),
    mlg151 character varying(255),
    rating character varying(255),
    ratedate character varying(255),
    phy_barrio character varying(255),
    mai_barrio character varying(255),
    mcsipstep character varying(255),
    mcsipdate character varying(255),
    userid character varying(255),
    addcode character varying(255),
    upd_reas character varying(255),
    delcode character varying(255),
    mcs150mileageyear character varying(255),
    adddate character varying(255),
    chgndate character varying(255),
    deldate character varying(255),
    tot_cars character varying(255),
    version character varying(255),
    createdate character varying(255),
    adduserid character varying(255),
    deluserid character varying(255),
    mcs_150_date character varying(255),
    rec_update_flag character varying(255),
    emailaddress character varying(255),
    usdot_revoked_flag character varying(255),
    usdot_revoked_number character varying(255),
    company_rep1 character varying(255),
    company_rep2 character varying(255),
    id bigint NOT NULL
);


--
-- Name: census_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.census ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.census_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: censuses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.censuses (
    mcs150_date timestamp(6) without time zone,
    add_date date,
    status_code character varying(1),
    dot_number integer,
    dun_bradstreet_no character varying,
    phy_omc_region integer,
    safety_inv_terr character varying(2),
    carrier_operation character varying(1),
    business_org_id integer,
    mcs150_mileage bigint,
    mcs150_mileage_year character varying(4),
    mcs151_mileage bigint,
    total_cars integer,
    mcs150_update_code_id integer,
    prior_revoke_flag character varying(1),
    prior_revoke_dot_number integer,
    phone character varying,
    fax character varying,
    cell_phone character varying,
    company_officer_1 character varying,
    company_officer_2 character varying,
    business_org_desc character varying,
    truck_units integer,
    power_units integer,
    bus_units integer,
    fleetsize character varying(1),
    review_id integer,
    recordable_crash_rate numeric(6,3),
    mail_nationality_indicator character varying(1),
    phy_nationality_indicator character varying(1),
    phy_barrio character varying,
    mail_barrio character varying,
    carship character varying,
    docket1_prefix character varying(2),
    docket1 integer,
    docket2_prefix character varying(2),
    docket2 integer,
    docket3_prefix character varying(2),
    docket3 integer,
    pointnum character varying,
    total_intrastate_drivers integer,
    mcsipstep integer,
    mcsipdate date,
    hm_ind character varying(1),
    interstate_beyond_100_miles integer,
    interstate_within_100_miles integer,
    intrastate_beyond_100_miles integer,
    intrastate_within_100_miles integer,
    total_cdl integer,
    total_drivers integer,
    avg_drivers_leased_per_month integer,
    classdef character varying,
    legal_name character varying,
    dba_name character varying,
    phy_street character varying,
    phy_city character varying,
    phy_country character varying(2),
    phy_state character varying(2),
    phy_zip character varying(10),
    phy_cnty character varying(3),
    carrier_mailing_street character varying,
    carrier_mailing_state character varying(2),
    carrier_mailing_city character varying,
    carrier_mailing_country character varying(2),
    carrier_mailing_zip character varying(10),
    carrier_mailing_cnty character varying(3),
    carrier_mailing_und_date date,
    driver_inter_total integer,
    email_address character varying,
    review_type character varying,
    review_date date,
    safety_rating character varying(1),
    safety_rating_date date,
    undeliv_phy character varying(1),
    crgo_genfreight character varying(1),
    crgo_household character varying(1),
    crgo_metalsheet character varying(1),
    crgo_motoveh character varying(1),
    crgo_drivetow character varying(1),
    crgo_logpole character varying(1),
    crgo_bldgmat character varying(1),
    crgo_mobilehome character varying(1),
    crgo_machlrg character varying(1),
    crgo_produce character varying(1),
    crgo_liqgas character varying(1),
    crgo_intermodal character varying(1),
    crgo_passengers character varying(1),
    crgo_oilfield character varying(1),
    crgo_livestock character varying(1),
    crgo_grainfeed character varying(1),
    crgo_coalcoke character varying(1),
    crgo_meat character varying(1),
    crgo_garbage character varying(1),
    crgo_usmail character varying(1),
    crgo_chem character varying(1),
    crgo_drybulk character varying(1),
    crgo_coldfood character varying(1),
    crgo_beverages character varying(1),
    crgo_paperprod character varying(1),
    crgo_utility character varying(1),
    crgo_farmsupp character varying(1),
    crgo_construct character varying(1),
    crgo_waterwell character varying(1),
    crgo_cargoothr character varying(1),
    crgo_cargoothr_desc character varying,
    owntruck integer,
    owntract integer,
    owntrail integer,
    owncoach integer,
    ownschool_1_8 integer,
    ownschool_9_15 integer,
    ownschool_16 integer,
    ownbus_16 integer,
    ownvan_1_8 integer,
    ownvan_9_15 integer,
    ownlimo_1_8 integer,
    ownlimo_9_15 integer,
    ownlimo_16 integer,
    trmtruck integer,
    trmtract integer,
    trmtrail integer,
    trmcoach integer,
    trmschool_1_8 integer,
    trmschool_9_15 integer,
    trmschool_16 integer,
    trmbus_16 integer,
    trmvan_1_8 integer,
    trmvan_9_15 integer,
    trmlimo_1_8 integer,
    trmlimo_9_15 integer,
    trmlimo_16 integer,
    trptruck integer,
    trptract integer,
    trptrail integer,
    trpcoach integer,
    trpschool_1_8 integer,
    trpschool_9_15 integer,
    trpschool_16 integer,
    trpbus_16 integer,
    trpvan_1_8 integer,
    trpvan_9_15 integer,
    trplimo_1_8 integer,
    trplimo_9_15 integer,
    trplimo_16 integer,
    id bigint NOT NULL
);


--
-- Name: censuses_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.censuses ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.censuses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: cities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cities (
    id bigint NOT NULL,
    latitude double precision NOT NULL,
    longitude double precision NOT NULL,
    slug character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    population integer,
    name character varying NOT NULL,
    ascii_name character varying NOT NULL,
    state_code character varying NOT NULL,
    country_code character varying NOT NULL
);


--
-- Name: cities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.cities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.cities_id_seq OWNED BY public.cities.id;


--
-- Name: cities_metadata; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cities_metadata (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    description text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: cities_metadata_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.cities_metadata_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cities_metadata_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.cities_metadata_id_seq OWNED BY public.cities_metadata.id;


--
-- Name: cities_postal_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cities_postal_codes (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    postal_code_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: cities_postal_codes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.cities_postal_codes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cities_postal_codes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.cities_postal_codes_id_seq OWNED BY public.cities_postal_codes.id;


--
-- Name: city_freights; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.city_freights (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    freight_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: city_freights_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.city_freights_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: city_freights_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.city_freights_id_seq OWNED BY public.city_freights.id;


--
-- Name: city_shipment_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.city_shipment_types (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    shipment_type_id bigint NOT NULL
);


--
-- Name: city_shipment_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.city_shipment_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: city_shipment_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.city_shipment_types_id_seq OWNED BY public.city_shipment_types.id;


--
-- Name: city_specialized_services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.city_specialized_services (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    specialized_service_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: city_specialized_services_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.city_specialized_services_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: city_specialized_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.city_specialized_services_id_seq OWNED BY public.city_specialized_services.id;


--
-- Name: city_truck_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.city_truck_types (
    id bigint NOT NULL,
    city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    truck_type_id bigint NOT NULL
);


--
-- Name: city_truck_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.city_truck_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: city_truck_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.city_truck_types_id_seq OWNED BY public.city_truck_types.id;


--
-- Name: claim_partners; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.claim_partners (
    id bigint NOT NULL,
    name character varying NOT NULL,
    description text NOT NULL,
    slug character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: claim_partners_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.claim_partners_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: claim_partners_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.claim_partners_id_seq OWNED BY public.claim_partners.id;


--
-- Name: companies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.companies (
    id bigint NOT NULL,
    dot_number integer NOT NULL,
    slug character varying NOT NULL,
    email public.citext,
    phone character varying,
    street character varying,
    city_name character varying,
    state_code character varying,
    country_code character varying,
    zip character varying,
    company_rep1 character varying,
    company_rep2 character varying,
    power_units integer,
    safety_rating character varying,
    safety_rating_date date,
    carrier_operation character varying,
    hazmat_indicator boolean DEFAULT false NOT NULL,
    mcs_150_mileage numeric,
    mcs_150_mileage_year integer,
    claim_token uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    legal_name character varying,
    dba_name character varying,
    hidden boolean DEFAULT false NOT NULL,
    name_field character varying DEFAULT 'dba_name'::character varying NOT NULL,
    requested_name character varying,
    name character varying,
    passengers_only boolean DEFAULT false NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    census_change_date timestamp(6) without time zone,
    city_id bigint,
    active_in_census boolean DEFAULT false NOT NULL
);


--
-- Name: companies_freights; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.companies_freights (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    freight_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: companies_freights_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.companies_freights_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: companies_freights_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.companies_freights_id_seq OWNED BY public.companies_freights.id;


--
-- Name: companies_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.companies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: companies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.companies_id_seq OWNED BY public.companies.id;


--
-- Name: companies_related_companies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.companies_related_companies (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    related_company_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: companies_related_companies_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.companies_related_companies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: companies_related_companies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.companies_related_companies_id_seq OWNED BY public.companies_related_companies.id;


--
-- Name: company_entity_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_entity_types (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    entity_type public.entity_type NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: company_entity_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.company_entity_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: company_entity_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.company_entity_types_id_seq OWNED BY public.company_entity_types.id;


--
-- Name: company_notes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.company_notes (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    note text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    company_id bigint NOT NULL,
    entity_type character varying NOT NULL
);


--
-- Name: company_notes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.company_notes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: company_notes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.company_notes_id_seq OWNED BY public.company_notes.id;


--
-- Name: crash_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.crash ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.crash_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: data_uploads; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_uploads (
    "timestamp" timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    file_names character varying(255),
    status character varying(255) DEFAULT 'pending'::character varying,
    type character varying(255),
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    id bigint NOT NULL
);


--
-- Name: data_uploads_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.data_uploads_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: data_uploads_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.data_uploads_id_seq OWNED BY public.data_uploads.id;


--
-- Name: developer_account_permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.developer_account_permissions (
    id bigint NOT NULL,
    developer_account_id bigint NOT NULL,
    endpoint character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: developer_account_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.developer_account_permissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: developer_account_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.developer_account_permissions_id_seq OWNED BY public.developer_account_permissions.id;


--
-- Name: developer_account_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.developer_account_users (
    id bigint NOT NULL,
    developer_account_id bigint NOT NULL,
    user_id bigint NOT NULL,
    api_token character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: developer_account_users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.developer_account_users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: developer_account_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.developer_account_users_id_seq OWNED BY public.developer_account_users.id;


--
-- Name: developer_accounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.developer_accounts (
    id bigint NOT NULL,
    name character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: developer_accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.developer_accounts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: developer_accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.developer_accounts_id_seq OWNED BY public.developer_accounts.id;


--
-- Name: domain_certificates; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.domain_certificates (
    id bigint NOT NULL,
    domain character varying NOT NULL,
    cloudflare_id character varying,
    heroku_id character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    render_id character varying
);


--
-- Name: domain_certificates_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.domain_certificates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: domain_certificates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.domain_certificates_id_seq OWNED BY public.domain_certificates.id;


--
-- Name: driver_carrier_reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_carrier_reviews (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    company_id bigint NOT NULL,
    utm_param_id bigint,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    rejection_reason character varying,
    nps integer,
    title character varying,
    started_on date,
    equipment integer,
    pay integer,
    home_time integer,
    training integer,
    management integer,
    benefits integer,
    safety integer,
    body text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    current_driver boolean,
    ended_on date,
    employment_type character varying,
    route_types character varying[] DEFAULT '{}'::character varying[],
    submitted_at timestamp(6) without time zone,
    truck_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    shipment_types bigint[] DEFAULT '{}'::bigint[] NOT NULL
);


--
-- Name: driver_carrier_reviews_aggregates; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_carrier_reviews_aggregates (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    star_rating numeric(3,2),
    review_count integer,
    nps numeric(3,2),
    equipment numeric(3,2),
    pay numeric(3,2),
    home_time numeric(3,2),
    training numeric(3,2),
    management numeric(3,2),
    benefits numeric(3,2),
    safety numeric(3,2),
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: driver_carrier_reviews_aggregates_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_carrier_reviews_aggregates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_carrier_reviews_aggregates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_carrier_reviews_aggregates_id_seq OWNED BY public.driver_carrier_reviews_aggregates.id;


--
-- Name: driver_carrier_reviews_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_carrier_reviews_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_carrier_reviews_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_carrier_reviews_id_seq OWNED BY public.driver_carrier_reviews.id;


--
-- Name: driver_employment_experiences; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_employment_experiences (
    id bigint NOT NULL,
    title character varying NOT NULL,
    company character varying NOT NULL,
    length character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    driver_job_application_id bigint NOT NULL
);


--
-- Name: driver_employment_experiences_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_employment_experiences_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_employment_experiences_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_employment_experiences_id_seq OWNED BY public.driver_employment_experiences.id;


--
-- Name: driver_interview_availabilities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_interview_availabilities (
    id bigint NOT NULL,
    day character varying NOT NULL,
    "time" character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    driver_job_application_id bigint NOT NULL
);


--
-- Name: driver_interview_availabilities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_interview_availabilities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_interview_availabilities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_interview_availabilities_id_seq OWNED BY public.driver_interview_availabilities.id;


--
-- Name: driver_job_applications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_job_applications (
    id bigint NOT NULL,
    driver_job_id bigint NOT NULL,
    city_id bigint NOT NULL,
    utm_param_id bigint,
    first_name character varying NOT NULL,
    last_name character varying NOT NULL,
    email public.citext NOT NULL,
    phone character varying NOT NULL,
    experience_years integer NOT NULL,
    valid_cdl boolean NOT NULL,
    authorized boolean NOT NULL,
    comment text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: driver_job_applications_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_job_applications_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_job_applications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_job_applications_id_seq OWNED BY public.driver_job_applications.id;


--
-- Name: driver_jobs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_jobs (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    title character varying NOT NULL,
    published boolean DEFAULT false NOT NULL,
    salary_min numeric,
    salary_max numeric,
    salary_unit character varying,
    employment_mode character varying,
    operating_schedule character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    summary text
);


--
-- Name: driver_jobs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_jobs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_jobs_id_seq OWNED BY public.driver_jobs.id;


--
-- Name: email_domains; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.email_domains (
    id bigint NOT NULL,
    domain public.citext NOT NULL,
    disposable boolean DEFAULT false NOT NULL,
    free boolean DEFAULT false NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: email_domains_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.email_domains_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: email_domains_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.email_domains_id_seq OWNED BY public.email_domains.id;


--
-- Name: email_feedbacks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.email_feedbacks (
    id bigint NOT NULL,
    email public.citext NOT NULL,
    feedback_type character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: email_feedbacks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.email_feedbacks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: email_feedbacks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.email_feedbacks_id_seq OWNED BY public.email_feedbacks.id;


--
-- Name: filer; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.filer (
    insurance_company character varying(255),
    attention_to_name character varying(255),
    company_name character varying(255),
    street character varying(255),
    city character varying(255),
    state character varying(2),
    country character varying(2),
    zip character varying(10),
    phone character varying(255),
    fax character varying(255),
    id bigint NOT NULL
);


--
-- Name: filer_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.filer ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.filer_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: freights; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.freights (
    id integer NOT NULL,
    name character varying(255),
    header character varying NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    slug character varying NOT NULL,
    modifiable boolean DEFAULT true NOT NULL
);


--
-- Name: freights_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.freights_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: freights_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.freights_id_seq OWNED BY public.freights.id;


--
-- Name: friendly_id_slugs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.friendly_id_slugs (
    id bigint NOT NULL,
    slug character varying NOT NULL,
    sluggable_id bigint NOT NULL,
    sluggable_type character varying(50),
    scope character varying,
    created_at timestamp(6) without time zone
);


--
-- Name: friendly_id_slugs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.friendly_id_slugs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: friendly_id_slugs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.friendly_id_slugs_id_seq OWNED BY public.friendly_id_slugs.id;


--
-- Name: incoming_webhook_events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.incoming_webhook_events (
    id bigint NOT NULL,
    source character varying NOT NULL,
    external_id character varying NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    data jsonb DEFAULT '{}'::jsonb NOT NULL,
    processing_errors text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: incoming_webhook_events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.incoming_webhook_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: incoming_webhook_events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.incoming_webhook_events_id_seq OWNED BY public.incoming_webhook_events.id;


--
-- Name: inspections; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.inspections (
    dot_number integer NOT NULL,
    inspections integer,
    driver_insp integer,
    driver_oos integer,
    vehicle_insp integer,
    vehicle_oos integer,
    states text,
    id bigint NOT NULL
);


--
-- Name: inspections_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.inspections ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.inspections_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: insurance; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.insurance (
    docket_number character varying(255),
    insurance_company character varying(255),
    insurance_type text,
    bi_pd_class character varying(1),
    bi_pd_max_limit integer,
    bi_pd_underlying_limit integer,
    policy_number character varying(25),
    effective_date date,
    form_code character varying(3),
    insurance_company_name character varying,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    id bigint NOT NULL,
    CONSTRAINT insurance_insurance_type_check CHECK ((insurance_type = ANY (ARRAY['BIPD'::text, 'CARGO'::text, 'BOND'::text, 'TRUST_FUND'::text, 'BIPD_PRIMARY'::text, 'BIPD_EXCESS'::text])))
);


--
-- Name: insurance_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.insurance ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.insurance_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: lists; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.lists (
    name character varying(255) NOT NULL,
    is_public boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_default boolean DEFAULT false,
    user_id bigint NOT NULL,
    id bigint NOT NULL,
    bookmarks_count integer DEFAULT 0
);


--
-- Name: lists_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.lists_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: lists_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.lists_id_seq OWNED BY public.lists.id;


--
-- Name: page_views; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.page_views (
    id bigint NOT NULL,
    date date NOT NULL,
    page_views integer DEFAULT 0 NOT NULL,
    average_session_duration integer DEFAULT 0 NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    company_id bigint NOT NULL
);


--
-- Name: monthly_page_views; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.monthly_page_views AS
 SELECT (date_trunc('month'::text, (date)::timestamp with time zone))::date AS date,
    company_id,
    sum(page_views) AS page_views,
    sum(average_session_duration) AS average_session_duration
   FROM public.page_views
  GROUP BY ((date_trunc('month'::text, (date)::timestamp with time zone))::date), company_id
  WITH NO DATA;


--
-- Name: one_off_tasks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.one_off_tasks (
    id bigint NOT NULL,
    version character varying NOT NULL
);


--
-- Name: one_off_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.one_off_tasks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: one_off_tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.one_off_tasks_id_seq OWNED BY public.one_off_tasks.id;


--
-- Name: operating_authorities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.operating_authorities (
    dot_number integer NOT NULL,
    docket_number character varying NOT NULL,
    pending_common_authority boolean DEFAULT false NOT NULL,
    pending_contract_authority boolean DEFAULT false NOT NULL,
    pending_broker_authority boolean DEFAULT false NOT NULL,
    common_authority_revocation boolean DEFAULT false NOT NULL,
    contract_authority_revocation boolean DEFAULT false NOT NULL,
    broker_authority_revocation boolean DEFAULT false NOT NULL,
    freight_flag boolean DEFAULT false NOT NULL,
    household_goods boolean DEFAULT false NOT NULL,
    bipd_required integer NOT NULL,
    cargo_required boolean DEFAULT false NOT NULL,
    bond_required boolean DEFAULT false NOT NULL,
    bipd_on_file integer NOT NULL,
    cargo_on_file boolean DEFAULT false NOT NULL,
    bond_on_file boolean DEFAULT false NOT NULL,
    dba_name character varying,
    legal_name character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    common_authority public.citext NOT NULL,
    contract_authority public.citext NOT NULL,
    broker_authority public.citext NOT NULL,
    id bigint NOT NULL
);


--
-- Name: operating_authorities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.operating_authorities ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.operating_authorities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: page_views_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.page_views_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: page_views_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.page_views_id_seq OWNED BY public.page_views.id;


--
-- Name: persona_verifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.persona_verifications (
    id bigint NOT NULL,
    persona_id bigint NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    company character varying NOT NULL,
    title character varying NOT NULL,
    phone character varying NOT NULL,
    email character varying,
    linkedin character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: persona_verifications_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.persona_verifications_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: persona_verifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.persona_verifications_id_seq OWNED BY public.persona_verifications.id;


--
-- Name: personas; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.personas (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    company_id bigint,
    type character varying NOT NULL,
    city_id bigint,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    verification_token character varying,
    verified_at timestamp(6) without time zone
);


--
-- Name: personas_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.personas_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: personas_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.personas_id_seq OWNED BY public.personas.id;


--
-- Name: postal_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.postal_codes (
    id bigint NOT NULL,
    code character varying NOT NULL,
    country_code character varying NOT NULL,
    latitude double precision,
    longitude double precision,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    acceptable_cities character varying[] DEFAULT '{}'::character varying[]
);


--
-- Name: postal_codes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.postal_codes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: postal_codes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.postal_codes_id_seq OWNED BY public.postal_codes.id;


--
-- Name: preferred_lanes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.preferred_lanes (
    id bigint NOT NULL,
    carrier_profile_id bigint NOT NULL,
    pickup_city_id bigint NOT NULL,
    dropoff_city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: preferred_lanes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.preferred_lanes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: preferred_lanes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.preferred_lanes_id_seq OWNED BY public.preferred_lanes.id;


--
-- Name: recently_vieweds; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.recently_vieweds (
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    user_id bigint,
    company_id bigint NOT NULL,
    id bigint NOT NULL
);


--
-- Name: recently_vieweds_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.recently_vieweds_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: recently_vieweds_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.recently_vieweds_id_seq OWNED BY public.recently_vieweds.id;


--
-- Name: replies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.replies (
    comment text,
    review_id integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    user_id bigint,
    id bigint NOT NULL
);


--
-- Name: replies_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.replies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: replies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.replies_id_seq OWNED BY public.replies.id;


--
-- Name: reports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reports (
    description text,
    reason character varying(255),
    review_id integer,
    dismissed boolean DEFAULT false,
    dismissed_by_carrier boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    user_id bigint,
    id bigint NOT NULL
);


--
-- Name: reports_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.reports_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: reports_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.reports_id_seq OWNED BY public.reports.id;


--
-- Name: request_for_proposals; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.request_for_proposals (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    user_id bigint,
    company_name character varying NOT NULL,
    name character varying NOT NULL,
    email public.citext NOT NULL,
    phone character varying NOT NULL,
    pickup_date date,
    pickup_city_id bigint,
    dropoff_date date,
    dropoff_city_id bigint,
    description text,
    marketing_opt_in boolean DEFAULT false NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    industry_id bigint,
    shipment_type_id bigint,
    specialized_service_id bigint,
    frequency character varying,
    frequency_other character varying,
    entity_type character varying DEFAULT 'carrier'::character varying NOT NULL,
    truck_type_id bigint,
    freight_id bigint,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    notification_template character varying,
    notified_at timestamp(6) without time zone
);


--
-- Name: request_for_proposals_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.request_for_proposals_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: request_for_proposals_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.request_for_proposals_id_seq OWNED BY public.request_for_proposals.id;


--
-- Name: review_lanes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.review_lanes (
    id bigint NOT NULL,
    review_id bigint NOT NULL,
    pickup_city_id bigint NOT NULL,
    dropoff_city_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: review_lanes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.review_lanes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: review_lanes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.review_lanes_id_seq OWNED BY public.review_lanes.id;


--
-- Name: review_sentiments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.review_sentiments (
    id bigint NOT NULL,
    review_id bigint NOT NULL,
    sentiment_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: review_sentiments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.review_sentiments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: review_sentiments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.review_sentiments_id_seq OWNED BY public.review_sentiments.id;


--
-- Name: reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reviews (
    id integer NOT NULL,
    likes text,
    dislikes text,
    timeliness integer,
    cleanliness integer,
    communication integer,
    consider_expensive integer,
    is_consider_next_time boolean,
    status public.citext DEFAULT 'pending'::public.citext,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    review_notes text,
    anonymous boolean,
    pickup_state character varying(255),
    pickup_city character varying(255),
    dropoff_state character varying(255),
    dropoff_city character varying(255),
    how_often integer,
    rejection_reason character varying(255),
    offer_electronic_tracking boolean,
    electronic_tracking_worked boolean,
    related_to_carrier boolean,
    offer_reference boolean,
    is_reference boolean DEFAULT false,
    featured boolean DEFAULT false,
    specialized_services bigint[] DEFAULT '{}'::bigint[],
    user_id bigint NOT NULL,
    utm_param_id bigint,
    freights integer[] DEFAULT '{}'::integer[],
    title character varying,
    body text,
    last_worked_with date,
    carrier_discovery character varying,
    form_version integer DEFAULT 2 NOT NULL,
    receive_availability_updates boolean,
    company_id bigint,
    star_rating numeric(3,2),
    submitted_at timestamp(6) without time zone,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    persona_id bigint NOT NULL,
    truck_types bigint[] DEFAULT '{}'::bigint[] NOT NULL,
    shipment_types bigint[] DEFAULT '{}'::bigint[] NOT NULL
);


--
-- Name: reviews_aggregates; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reviews_aggregates (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    star_rating numeric,
    review_count integer,
    offer_electronic_tracking numeric,
    electronic_tracking_worked numeric,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    timeliness numeric(4,2),
    cleanliness numeric(4,2),
    communication numeric(4,2),
    sentiments jsonb
);


--
-- Name: reviews_aggregates_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.reviews_aggregates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: reviews_aggregates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.reviews_aggregates_id_seq OWNED BY public.reviews_aggregates.id;


--
-- Name: reviews_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.reviews_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: reviews_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.reviews_id_seq OWNED BY public.reviews.id;


--
-- Name: role_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.role_actions (
    id bigint NOT NULL,
    role_id bigint NOT NULL,
    subject character varying NOT NULL,
    action character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: role_actions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.role_actions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: role_actions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.role_actions_id_seq OWNED BY public.role_actions.id;


--
-- Name: roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles (
    id bigint NOT NULL,
    name character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: roles_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.roles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.roles_id_seq OWNED BY public.roles.id;


--
-- Name: rollups; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.rollups (
    id bigint NOT NULL,
    name character varying NOT NULL,
    "interval" character varying NOT NULL,
    "time" timestamp(6) without time zone NOT NULL,
    dimensions jsonb DEFAULT '{}'::jsonb NOT NULL,
    value double precision
);


--
-- Name: rollups_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.rollups_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: rollups_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.rollups_id_seq OWNED BY public.rollups.id;


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version character varying NOT NULL
);


--
-- Name: sentiment_personas; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sentiment_personas (
    id bigint NOT NULL,
    sentiment_id bigint NOT NULL,
    reviewer character varying NOT NULL,
    reviewee character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: sentiment_personas_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sentiment_personas_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sentiment_personas_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sentiment_personas_id_seq OWNED BY public.sentiment_personas.id;


--
-- Name: sentiments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sentiments (
    id bigint NOT NULL,
    label character varying NOT NULL,
    positive boolean NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    slug character varying NOT NULL
);


--
-- Name: sentiments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sentiments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sentiments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sentiments_id_seq OWNED BY public.sentiments.id;


--
-- Name: shipment_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shipment_types (
    key character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    slug character varying NOT NULL,
    row_order integer,
    name character varying NOT NULL,
    modifiable boolean DEFAULT true NOT NULL,
    id bigint NOT NULL
);


--
-- Name: shipment_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shipment_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shipment_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shipment_types_id_seq OWNED BY public.shipment_types.id;


--
-- Name: specialized_services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.specialized_services (
    id bigint NOT NULL,
    name character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    slug character varying NOT NULL,
    row_order integer,
    modifiable boolean DEFAULT true NOT NULL
);


--
-- Name: specialized_services_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.specialized_services_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: specialized_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.specialized_services_id_seq OWNED BY public.specialized_services.id;


--
-- Name: subscriptions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.subscriptions (
    id bigint NOT NULL,
    external_id character varying NOT NULL,
    status character varying NOT NULL,
    start_date timestamp without time zone NOT NULL,
    ended_at timestamp without time zone,
    canceled_at timestamp without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    resource_type character varying,
    resource_id bigint
);


--
-- Name: subscriptions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.subscriptions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: subscriptions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.subscriptions_id_seq OWNED BY public.subscriptions.id;


--
-- Name: truck_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.truck_types (
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255),
    key character varying NOT NULL,
    slug character varying NOT NULL,
    row_order integer,
    modifiable boolean DEFAULT true NOT NULL,
    id bigint NOT NULL
);


--
-- Name: truck_types_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.truck_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: truck_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.truck_types_id_seq OWNED BY public.truck_types.id;


--
-- Name: user_emails; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_emails (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    email public.citext NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    verification_token character varying,
    verified_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: user_emails_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.user_emails_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: user_emails_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.user_emails_id_seq OWNED BY public.user_emails.id;


--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_roles (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    role_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: user_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.user_roles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: user_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.user_roles_id_seq OWNED BY public.user_roles.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    first_name character varying NOT NULL,
    last_name character varying NOT NULL,
    email public.citext NOT NULL,
    encrypted_password character varying,
    confirmation_token character varying,
    remember_token character varying NOT NULL,
    verified boolean DEFAULT false NOT NULL,
    verification_token character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    admin boolean DEFAULT false NOT NULL,
    company character varying,
    current_login_ip inet,
    blocked boolean DEFAULT false NOT NULL,
    stripe_customer_id character varying,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    phone character varying,
    analytics_company_id bigint
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: utm_params; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.utm_params (
    id bigint NOT NULL,
    source character varying NOT NULL,
    medium character varying,
    term character varying,
    content character varying,
    campaign character varying,
    "from" character varying,
    "time" character varying,
    lp character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: utm_params_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.utm_params_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: utm_params_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.utm_params_id_seq OWNED BY public.utm_params.id;


--
-- Name: versions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.versions (
    id bigint NOT NULL,
    item_type character varying NOT NULL,
    item_id bigint NOT NULL,
    event character varying NOT NULL,
    user_id bigint,
    request_uuid character varying,
    whodunnit character varying,
    object jsonb,
    object_changes jsonb,
    created_at timestamp(6) without time zone
);


--
-- Name: versions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.versions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: versions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.versions_id_seq OWNED BY public.versions.id;


--
-- Name: violation_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

ALTER TABLE public.violation ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.violation_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: webhooks_events; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.webhooks_events (
    id bigint NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    type character varying NOT NULL,
    data jsonb NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: webhooks_events_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.webhooks_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: webhooks_events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.webhooks_events_id_seq OWNED BY public.webhooks_events.id;


--
-- Name: webhooks_subscriptions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.webhooks_subscriptions (
    id bigint NOT NULL,
    developer_account_id bigint NOT NULL,
    url character varying NOT NULL,
    secret character varying NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    status character varying DEFAULT 'active'::character varying NOT NULL,
    types character varying[] DEFAULT '{}'::character varying[]
);


--
-- Name: webhooks_subscriptions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.webhooks_subscriptions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: webhooks_subscriptions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.webhooks_subscriptions_id_seq OWNED BY public.webhooks_subscriptions.id;


--
-- Name: weekly_page_views; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.weekly_page_views AS
 SELECT (date_trunc('week'::text, (date)::timestamp with time zone))::date AS date,
    company_id,
    sum(page_views) AS page_views,
    sum(average_session_duration) AS average_session_duration
   FROM public.page_views
  GROUP BY ((date_trunc('week'::text, (date)::timestamp with time zone))::date), company_id
  WITH NO DATA;


--
-- Name: widget_brokerage_reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.widget_brokerage_reviews (
    id bigint NOT NULL,
    widget_id bigint NOT NULL,
    brokerage_review_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: widget_brokerage_reviews_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.widget_brokerage_reviews_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: widget_brokerage_reviews_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.widget_brokerage_reviews_id_seq OWNED BY public.widget_brokerage_reviews.id;


--
-- Name: widget_reviews; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.widget_reviews (
    id bigint NOT NULL,
    widget_id bigint NOT NULL,
    review_id bigint NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: widget_reviews_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.widget_reviews_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: widget_reviews_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.widget_reviews_id_seq OWNED BY public.widget_reviews.id;


--
-- Name: widgets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.widgets (
    id bigint NOT NULL,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    widget_type character varying NOT NULL,
    settings jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    company_id bigint NOT NULL,
    entity_type character varying NOT NULL,
    enabled boolean DEFAULT false NOT NULL
);


--
-- Name: widgets_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.widgets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: widgets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.widgets_id_seq OWNED BY public.widgets.id;


--
-- Name: analytics_aggregate_shipper_events_2025_02; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_02 FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');


--
-- Name: analytics_aggregate_shipper_events_2025_03; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_03 FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');


--
-- Name: analytics_aggregate_shipper_events_2025_04; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_04 FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');


--
-- Name: analytics_aggregate_shipper_events_2025_05; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_05 FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');


--
-- Name: analytics_aggregate_shipper_events_2025_06; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_06 FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');


--
-- Name: analytics_aggregate_shipper_events_2025_07; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_07 FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');


--
-- Name: analytics_aggregate_shipper_events_2025_08; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_08 FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');


--
-- Name: analytics_aggregate_shipper_events_2025_09; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_09 FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');


--
-- Name: analytics_aggregate_shipper_events_2025_10; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_10 FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');


--
-- Name: analytics_events_2024_09; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2024_09 FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-10-01 00:00:00');


--
-- Name: analytics_events_2024_10; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2024_10 FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-11-01 00:00:00');


--
-- Name: analytics_events_2024_11; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2024_11 FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-12-01 00:00:00');


--
-- Name: analytics_events_2024_12; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2024_12 FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2025-01-01 00:00:00');


--
-- Name: analytics_events_2025_01; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_01 FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2025-02-01 00:00:00');


--
-- Name: analytics_events_2025_02; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_02 FOR VALUES FROM ('2025-02-01 00:00:00') TO ('2025-03-01 00:00:00');


--
-- Name: analytics_events_2025_03; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_03 FOR VALUES FROM ('2025-03-01 00:00:00') TO ('2025-04-01 00:00:00');


--
-- Name: analytics_events_2025_04; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_04 FOR VALUES FROM ('2025-04-01 00:00:00') TO ('2025-05-01 00:00:00');


--
-- Name: analytics_events_2025_05; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_05 FOR VALUES FROM ('2025-05-01 00:00:00') TO ('2025-06-01 00:00:00');


--
-- Name: analytics_events_2025_06; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_06 FOR VALUES FROM ('2025-06-01 00:00:00') TO ('2025-07-01 00:00:00');


--
-- Name: analytics_events_2025_07; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_07 FOR VALUES FROM ('2025-07-01 00:00:00') TO ('2025-08-01 00:00:00');


--
-- Name: analytics_events_2025_08; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_08 FOR VALUES FROM ('2025-08-01 00:00:00') TO ('2025-09-01 00:00:00');


--
-- Name: analytics_events_2025_09; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_09 FOR VALUES FROM ('2025-09-01 00:00:00') TO ('2025-10-01 00:00:00');


--
-- Name: analytics_events_2025_10; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ATTACH PARTITION public.analytics_events_2025_10 FOR VALUES FROM ('2025-10-01 00:00:00') TO ('2025-11-01 00:00:00');


--
-- Name: analytics_shipper_events_2024_09; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2024_09 FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-10-01 00:00:00');


--
-- Name: analytics_shipper_events_2024_10; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2024_10 FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-11-01 00:00:00');


--
-- Name: analytics_shipper_events_2024_11; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2024_11 FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-12-01 00:00:00');


--
-- Name: analytics_shipper_events_2024_12; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2024_12 FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2025-01-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_01; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_01 FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2025-02-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_02; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_02 FOR VALUES FROM ('2025-02-01 00:00:00') TO ('2025-03-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_03; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_03 FOR VALUES FROM ('2025-03-01 00:00:00') TO ('2025-04-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_04; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_04 FOR VALUES FROM ('2025-04-01 00:00:00') TO ('2025-05-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_05; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_05 FOR VALUES FROM ('2025-05-01 00:00:00') TO ('2025-06-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_06; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_06 FOR VALUES FROM ('2025-06-01 00:00:00') TO ('2025-07-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_07; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_07 FOR VALUES FROM ('2025-07-01 00:00:00') TO ('2025-08-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_08; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_08 FOR VALUES FROM ('2025-08-01 00:00:00') TO ('2025-09-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_09; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_09 FOR VALUES FROM ('2025-09-01 00:00:00') TO ('2025-10-01 00:00:00');


--
-- Name: analytics_shipper_events_2025_10; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ATTACH PARTITION public.analytics_shipper_events_2025_10 FOR VALUES FROM ('2025-10-01 00:00:00') TO ('2025-11-01 00:00:00');


--
-- Name: analytics_visits_2024_09; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2024_09 FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-10-01 00:00:00');


--
-- Name: analytics_visits_2024_10; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2024_10 FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-11-01 00:00:00');


--
-- Name: analytics_visits_2024_11; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2024_11 FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-12-01 00:00:00');


--
-- Name: analytics_visits_2024_12; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2024_12 FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2025-01-01 00:00:00');


--
-- Name: analytics_visits_2025_01; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_01 FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2025-02-01 00:00:00');


--
-- Name: analytics_visits_2025_02; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_02 FOR VALUES FROM ('2025-02-01 00:00:00') TO ('2025-03-01 00:00:00');


--
-- Name: analytics_visits_2025_03; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_03 FOR VALUES FROM ('2025-03-01 00:00:00') TO ('2025-04-01 00:00:00');


--
-- Name: analytics_visits_2025_04; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_04 FOR VALUES FROM ('2025-04-01 00:00:00') TO ('2025-05-01 00:00:00');


--
-- Name: analytics_visits_2025_05; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_05 FOR VALUES FROM ('2025-05-01 00:00:00') TO ('2025-06-01 00:00:00');


--
-- Name: analytics_visits_2025_06; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_06 FOR VALUES FROM ('2025-06-01 00:00:00') TO ('2025-07-01 00:00:00');


--
-- Name: analytics_visits_2025_07; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_07 FOR VALUES FROM ('2025-07-01 00:00:00') TO ('2025-08-01 00:00:00');


--
-- Name: analytics_visits_2025_08; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_08 FOR VALUES FROM ('2025-08-01 00:00:00') TO ('2025-09-01 00:00:00');


--
-- Name: analytics_visits_2025_09; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_09 FOR VALUES FROM ('2025-09-01 00:00:00') TO ('2025-10-01 00:00:00');


--
-- Name: analytics_visits_2025_10; Type: TABLE ATTACH; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ATTACH PARTITION public.analytics_visits_2025_10 FOR VALUES FROM ('2025-10-01 00:00:00') TO ('2025-11-01 00:00:00');


--
-- Name: access_package_allotments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.access_package_allotments ALTER COLUMN id SET DEFAULT nextval('public.access_package_allotments_id_seq'::regclass);


--
-- Name: access_packages id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.access_packages ALTER COLUMN id SET DEFAULT nextval('public.access_packages_id_seq'::regclass);


--
-- Name: action_text_rich_texts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.action_text_rich_texts ALTER COLUMN id SET DEFAULT nextval('public.action_text_rich_texts_id_seq'::regclass);


--
-- Name: active_admin_comments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_admin_comments ALTER COLUMN id SET DEFAULT nextval('public.active_admin_comments_id_seq'::regclass);


--
-- Name: active_campaign_records id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_campaign_records ALTER COLUMN id SET DEFAULT nextval('public.active_campaign_records_id_seq'::regclass);


--
-- Name: active_storage_attachments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_attachments ALTER COLUMN id SET DEFAULT nextval('public.active_storage_attachments_id_seq'::regclass);


--
-- Name: active_storage_blobs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_blobs ALTER COLUMN id SET DEFAULT nextval('public.active_storage_blobs_id_seq'::regclass);


--
-- Name: active_storage_variant_records id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_variant_records ALTER COLUMN id SET DEFAULT nextval('public.active_storage_variant_records_id_seq'::regclass);


--
-- Name: analytics_aggregate_shipper_events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_aggregate_shipper_events ALTER COLUMN id SET DEFAULT nextval('public.analytics_aggregate_shipper_events_id_seq'::regclass);


--
-- Name: analytics_companies id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_companies ALTER COLUMN id SET DEFAULT nextval('public.analytics_companies_id_seq'::regclass);


--
-- Name: analytics_company_event_feed_exports id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_exports ALTER COLUMN id SET DEFAULT nextval('public.analytics_company_event_feed_exports_id_seq'::regclass);


--
-- Name: analytics_company_event_feed_notification_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notification_logs ALTER COLUMN id SET DEFAULT nextval('public.analytics_company_event_feed_notification_logs_id_seq'::regclass);


--
-- Name: analytics_company_event_feed_notifications id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notifications ALTER COLUMN id SET DEFAULT nextval('public.analytics_company_event_feed_notifications_id_seq'::regclass);


--
-- Name: analytics_company_event_feeds id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feeds ALTER COLUMN id SET DEFAULT nextval('public.analytics_company_event_feeds_id_seq'::regclass);


--
-- Name: analytics_company_providers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_providers ALTER COLUMN id SET DEFAULT nextval('public.analytics_company_providers_id_seq'::regclass);


--
-- Name: analytics_event_feeds id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_event_feeds ALTER COLUMN id SET DEFAULT nextval('public.analytics_event_feeds_id_seq'::regclass);


--
-- Name: analytics_events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events ALTER COLUMN id SET DEFAULT nextval('public.analytics_events_id_seq'::regclass);


--
-- Name: analytics_industries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_industries ALTER COLUMN id SET DEFAULT nextval('public.analytics_industries_id_seq'::regclass);


--
-- Name: analytics_integrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_integrations ALTER COLUMN id SET DEFAULT nextval('public.analytics_integrations_id_seq'::regclass);


--
-- Name: analytics_load_industries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_load_industries ALTER COLUMN id SET DEFAULT nextval('public.analytics_load_industries_id_seq'::regclass);


--
-- Name: analytics_shipper_events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_shipper_events ALTER COLUMN id SET DEFAULT nextval('public.analytics_shipper_events_id_seq'::regclass);


--
-- Name: analytics_visits id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits ALTER COLUMN id SET DEFAULT nextval('public.analytics_visits_id_seq'::regclass);


--
-- Name: api_tokens id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.api_tokens ALTER COLUMN id SET DEFAULT nextval('public.api_tokens_id_seq'::regclass);


--
-- Name: audits id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audits ALTER COLUMN id SET DEFAULT nextval('public.audits_id_seq'::regclass);


--
-- Name: authentications id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authentications ALTER COLUMN id SET DEFAULT nextval('public.authentications_id_seq'::regclass);


--
-- Name: blocked_ips id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.blocked_ips ALTER COLUMN id SET DEFAULT nextval('public.blocked_ips_id_seq'::regclass);


--
-- Name: bookmarks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bookmarks ALTER COLUMN id SET DEFAULT nextval('public.bookmarks_id_seq'::regclass);


--
-- Name: brokerage_cities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_cities ALTER COLUMN id SET DEFAULT nextval('public.brokerage_cities_id_seq'::regclass);


--
-- Name: brokerage_domains id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_domains ALTER COLUMN id SET DEFAULT nextval('public.brokerage_domains_id_seq'::regclass);


--
-- Name: brokerage_onboardings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_onboardings ALTER COLUMN id SET DEFAULT nextval('public.brokerage_onboardings_id_seq'::regclass);


--
-- Name: brokerage_profile_assets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_assets ALTER COLUMN id SET DEFAULT nextval('public.brokerage_profile_assets_id_seq'::regclass);


--
-- Name: brokerage_profile_contacts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_contacts ALTER COLUMN id SET DEFAULT nextval('public.brokerage_profile_contacts_id_seq'::regclass);


--
-- Name: brokerage_profile_preferred_lanes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_preferred_lanes ALTER COLUMN id SET DEFAULT nextval('public.brokerage_profile_preferred_lanes_id_seq'::regclass);


--
-- Name: brokerage_profile_users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_users ALTER COLUMN id SET DEFAULT nextval('public.brokerage_profile_users_id_seq'::regclass);


--
-- Name: brokerage_profile_widgets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_widgets ALTER COLUMN id SET DEFAULT nextval('public.brokerage_profile_widgets_id_seq'::regclass);


--
-- Name: brokerage_profiles id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profiles ALTER COLUMN id SET DEFAULT nextval('public.brokerage_profiles_id_seq'::regclass);


--
-- Name: brokerage_review_aggregates id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_aggregates ALTER COLUMN id SET DEFAULT nextval('public.brokerage_review_aggregates_id_seq'::regclass);


--
-- Name: brokerage_review_replies id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_replies ALTER COLUMN id SET DEFAULT nextval('public.brokerage_review_replies_id_seq'::regclass);


--
-- Name: brokerage_review_reports id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_reports ALTER COLUMN id SET DEFAULT nextval('public.brokerage_review_reports_id_seq'::regclass);


--
-- Name: brokerage_review_sentiments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_sentiments ALTER COLUMN id SET DEFAULT nextval('public.brokerage_review_sentiments_id_seq'::regclass);


--
-- Name: brokerage_reviews id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews ALTER COLUMN id SET DEFAULT nextval('public.brokerage_reviews_id_seq'::regclass);


--
-- Name: brokerage_reviews_review_lanes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews_review_lanes ALTER COLUMN id SET DEFAULT nextval('public.brokerage_reviews_review_lanes_id_seq'::regclass);


--
-- Name: brokerages_review_lanes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_review_lanes ALTER COLUMN id SET DEFAULT nextval('public.brokerages_review_lanes_id_seq'::regclass);


--
-- Name: brokerages_shipment_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_shipment_types ALTER COLUMN id SET DEFAULT nextval('public.brokerages_shipment_types_id_seq'::regclass);


--
-- Name: brokerages_specialized_services id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_specialized_services ALTER COLUMN id SET DEFAULT nextval('public.brokerages_specialized_services_id_seq'::regclass);


--
-- Name: brokerages_truck_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_truck_types ALTER COLUMN id SET DEFAULT nextval('public.brokerages_truck_types_id_seq'::regclass);


--
-- Name: carrier_availabilities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availabilities ALTER COLUMN id SET DEFAULT nextval('public.carrier_availabilities_id_seq'::regclass);


--
-- Name: carrier_availability_destinations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_destinations ALTER COLUMN id SET DEFAULT nextval('public.carrier_availability_destinations_id_seq'::regclass);


--
-- Name: carrier_availability_origins id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_origins ALTER COLUMN id SET DEFAULT nextval('public.carrier_availability_origins_id_seq'::regclass);


--
-- Name: carrier_availability_unsubscribes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_unsubscribes ALTER COLUMN id SET DEFAULT nextval('public.carrier_availability_unsubscribes_id_seq'::regclass);


--
-- Name: carrier_cities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_cities ALTER COLUMN id SET DEFAULT nextval('public.carrier_cities_id_seq'::regclass);


--
-- Name: carrier_csv_exports id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_csv_exports ALTER COLUMN id SET DEFAULT nextval('public.carrier_csv_exports_id_seq'::regclass);


--
-- Name: carrier_network_builder_lane_entities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_lane_entities ALTER COLUMN id SET DEFAULT nextval('public.carrier_network_builder_lane_entities_id_seq'::regclass);


--
-- Name: carrier_network_builder_lanes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_lanes ALTER COLUMN id SET DEFAULT nextval('public.carrier_network_builder_lanes_id_seq'::regclass);


--
-- Name: carrier_network_builder_messages id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_messages ALTER COLUMN id SET DEFAULT nextval('public.carrier_network_builder_messages_id_seq'::regclass);


--
-- Name: carrier_network_builders id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builders ALTER COLUMN id SET DEFAULT nextval('public.carrier_network_builders_id_seq'::regclass);


--
-- Name: carrier_operation_states id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_operation_states ALTER COLUMN id SET DEFAULT nextval('public.carrier_operation_states_id_seq'::regclass);


--
-- Name: carrier_profile_assets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_assets ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_assets_id_seq'::regclass);


--
-- Name: carrier_profile_contacts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_contacts ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_contacts_id_seq'::regclass);


--
-- Name: carrier_profile_terminals id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_terminals ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_terminals_id_seq'::regclass);


--
-- Name: carrier_profile_users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_users ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_users_id_seq'::regclass);


--
-- Name: carrier_profile_website_highlights id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_highlights ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_website_highlights_id_seq'::regclass);


--
-- Name: carrier_profile_website_reviews id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_reviews ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_website_reviews_id_seq'::regclass);


--
-- Name: carrier_profile_website_services id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_services ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_website_services_id_seq'::regclass);


--
-- Name: carrier_profile_websites id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_websites ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_websites_id_seq'::regclass);


--
-- Name: carrier_profile_widgets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_widgets ALTER COLUMN id SET DEFAULT nextval('public.carrier_profile_widgets_id_seq'::regclass);


--
-- Name: carrier_profiles id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profiles ALTER COLUMN id SET DEFAULT nextval('public.carrier_profiles_id_seq'::regclass);


--
-- Name: carriers_review_lanes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_review_lanes ALTER COLUMN id SET DEFAULT nextval('public.carriers_review_lanes_id_seq'::regclass);


--
-- Name: carriers_shipment_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_shipment_types ALTER COLUMN id SET DEFAULT nextval('public.carriers_shipment_types_id_seq'::regclass);


--
-- Name: carriers_specialized_services id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_specialized_services ALTER COLUMN id SET DEFAULT nextval('public.carriers_specialized_services_id_seq'::regclass);


--
-- Name: carriers_truck_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_truck_types ALTER COLUMN id SET DEFAULT nextval('public.carriers_truck_types_id_seq'::regclass);


--
-- Name: cities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities ALTER COLUMN id SET DEFAULT nextval('public.cities_id_seq'::regclass);


--
-- Name: cities_metadata id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_metadata ALTER COLUMN id SET DEFAULT nextval('public.cities_metadata_id_seq'::regclass);


--
-- Name: cities_postal_codes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_postal_codes ALTER COLUMN id SET DEFAULT nextval('public.cities_postal_codes_id_seq'::regclass);


--
-- Name: city_freights id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_freights ALTER COLUMN id SET DEFAULT nextval('public.city_freights_id_seq'::regclass);


--
-- Name: city_shipment_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_shipment_types ALTER COLUMN id SET DEFAULT nextval('public.city_shipment_types_id_seq'::regclass);


--
-- Name: city_specialized_services id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_specialized_services ALTER COLUMN id SET DEFAULT nextval('public.city_specialized_services_id_seq'::regclass);


--
-- Name: city_truck_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_truck_types ALTER COLUMN id SET DEFAULT nextval('public.city_truck_types_id_seq'::regclass);


--
-- Name: claim_partners id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.claim_partners ALTER COLUMN id SET DEFAULT nextval('public.claim_partners_id_seq'::regclass);


--
-- Name: companies id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies ALTER COLUMN id SET DEFAULT nextval('public.companies_id_seq'::regclass);


--
-- Name: companies_freights id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_freights ALTER COLUMN id SET DEFAULT nextval('public.companies_freights_id_seq'::regclass);


--
-- Name: companies_related_companies id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_related_companies ALTER COLUMN id SET DEFAULT nextval('public.companies_related_companies_id_seq'::regclass);


--
-- Name: company_entity_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_entity_types ALTER COLUMN id SET DEFAULT nextval('public.company_entity_types_id_seq'::regclass);


--
-- Name: company_notes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_notes ALTER COLUMN id SET DEFAULT nextval('public.company_notes_id_seq'::regclass);


--
-- Name: data_uploads id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_uploads ALTER COLUMN id SET DEFAULT nextval('public.data_uploads_id_seq'::regclass);


--
-- Name: developer_account_permissions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_account_permissions ALTER COLUMN id SET DEFAULT nextval('public.developer_account_permissions_id_seq'::regclass);


--
-- Name: developer_account_users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_account_users ALTER COLUMN id SET DEFAULT nextval('public.developer_account_users_id_seq'::regclass);


--
-- Name: developer_accounts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_accounts ALTER COLUMN id SET DEFAULT nextval('public.developer_accounts_id_seq'::regclass);


--
-- Name: domain_certificates id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.domain_certificates ALTER COLUMN id SET DEFAULT nextval('public.domain_certificates_id_seq'::regclass);


--
-- Name: driver_carrier_reviews id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews ALTER COLUMN id SET DEFAULT nextval('public.driver_carrier_reviews_id_seq'::regclass);


--
-- Name: driver_carrier_reviews_aggregates id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews_aggregates ALTER COLUMN id SET DEFAULT nextval('public.driver_carrier_reviews_aggregates_id_seq'::regclass);


--
-- Name: driver_employment_experiences id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_employment_experiences ALTER COLUMN id SET DEFAULT nextval('public.driver_employment_experiences_id_seq'::regclass);


--
-- Name: driver_interview_availabilities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_interview_availabilities ALTER COLUMN id SET DEFAULT nextval('public.driver_interview_availabilities_id_seq'::regclass);


--
-- Name: driver_job_applications id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_job_applications ALTER COLUMN id SET DEFAULT nextval('public.driver_job_applications_id_seq'::regclass);


--
-- Name: driver_jobs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_jobs ALTER COLUMN id SET DEFAULT nextval('public.driver_jobs_id_seq'::regclass);


--
-- Name: email_domains id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.email_domains ALTER COLUMN id SET DEFAULT nextval('public.email_domains_id_seq'::regclass);


--
-- Name: email_feedbacks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.email_feedbacks ALTER COLUMN id SET DEFAULT nextval('public.email_feedbacks_id_seq'::regclass);


--
-- Name: freights id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.freights ALTER COLUMN id SET DEFAULT nextval('public.freights_id_seq'::regclass);


--
-- Name: friendly_id_slugs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.friendly_id_slugs ALTER COLUMN id SET DEFAULT nextval('public.friendly_id_slugs_id_seq'::regclass);


--
-- Name: incoming_webhook_events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.incoming_webhook_events ALTER COLUMN id SET DEFAULT nextval('public.incoming_webhook_events_id_seq'::regclass);


--
-- Name: lists id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.lists ALTER COLUMN id SET DEFAULT nextval('public.lists_id_seq'::regclass);


--
-- Name: one_off_tasks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.one_off_tasks ALTER COLUMN id SET DEFAULT nextval('public.one_off_tasks_id_seq'::regclass);


--
-- Name: page_views id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.page_views ALTER COLUMN id SET DEFAULT nextval('public.page_views_id_seq'::regclass);


--
-- Name: persona_verifications id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.persona_verifications ALTER COLUMN id SET DEFAULT nextval('public.persona_verifications_id_seq'::regclass);


--
-- Name: personas id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personas ALTER COLUMN id SET DEFAULT nextval('public.personas_id_seq'::regclass);


--
-- Name: postal_codes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.postal_codes ALTER COLUMN id SET DEFAULT nextval('public.postal_codes_id_seq'::regclass);


--
-- Name: preferred_lanes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.preferred_lanes ALTER COLUMN id SET DEFAULT nextval('public.preferred_lanes_id_seq'::regclass);


--
-- Name: recently_vieweds id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.recently_vieweds ALTER COLUMN id SET DEFAULT nextval('public.recently_vieweds_id_seq'::regclass);


--
-- Name: replies id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.replies ALTER COLUMN id SET DEFAULT nextval('public.replies_id_seq'::regclass);


--
-- Name: reports id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reports ALTER COLUMN id SET DEFAULT nextval('public.reports_id_seq'::regclass);


--
-- Name: request_for_proposals id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals ALTER COLUMN id SET DEFAULT nextval('public.request_for_proposals_id_seq'::regclass);


--
-- Name: review_lanes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_lanes ALTER COLUMN id SET DEFAULT nextval('public.review_lanes_id_seq'::regclass);


--
-- Name: review_sentiments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_sentiments ALTER COLUMN id SET DEFAULT nextval('public.review_sentiments_id_seq'::regclass);


--
-- Name: reviews id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews ALTER COLUMN id SET DEFAULT nextval('public.reviews_id_seq'::regclass);


--
-- Name: reviews_aggregates id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews_aggregates ALTER COLUMN id SET DEFAULT nextval('public.reviews_aggregates_id_seq'::regclass);


--
-- Name: role_actions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_actions ALTER COLUMN id SET DEFAULT nextval('public.role_actions_id_seq'::regclass);


--
-- Name: roles id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles ALTER COLUMN id SET DEFAULT nextval('public.roles_id_seq'::regclass);


--
-- Name: rollups id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.rollups ALTER COLUMN id SET DEFAULT nextval('public.rollups_id_seq'::regclass);


--
-- Name: sentiment_personas id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sentiment_personas ALTER COLUMN id SET DEFAULT nextval('public.sentiment_personas_id_seq'::regclass);


--
-- Name: sentiments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sentiments ALTER COLUMN id SET DEFAULT nextval('public.sentiments_id_seq'::regclass);


--
-- Name: shipment_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipment_types ALTER COLUMN id SET DEFAULT nextval('public.shipment_types_id_seq'::regclass);


--
-- Name: specialized_services id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.specialized_services ALTER COLUMN id SET DEFAULT nextval('public.specialized_services_id_seq'::regclass);


--
-- Name: subscriptions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.subscriptions ALTER COLUMN id SET DEFAULT nextval('public.subscriptions_id_seq'::regclass);


--
-- Name: truck_types id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.truck_types ALTER COLUMN id SET DEFAULT nextval('public.truck_types_id_seq'::regclass);


--
-- Name: user_emails id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_emails ALTER COLUMN id SET DEFAULT nextval('public.user_emails_id_seq'::regclass);


--
-- Name: user_roles id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles ALTER COLUMN id SET DEFAULT nextval('public.user_roles_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: utm_params id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.utm_params ALTER COLUMN id SET DEFAULT nextval('public.utm_params_id_seq'::regclass);


--
-- Name: versions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.versions ALTER COLUMN id SET DEFAULT nextval('public.versions_id_seq'::regclass);


--
-- Name: webhooks_events id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.webhooks_events ALTER COLUMN id SET DEFAULT nextval('public.webhooks_events_id_seq'::regclass);


--
-- Name: webhooks_subscriptions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.webhooks_subscriptions ALTER COLUMN id SET DEFAULT nextval('public.webhooks_subscriptions_id_seq'::regclass);


--
-- Name: widget_brokerage_reviews id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_brokerage_reviews ALTER COLUMN id SET DEFAULT nextval('public.widget_brokerage_reviews_id_seq'::regclass);


--
-- Name: widget_reviews id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_reviews ALTER COLUMN id SET DEFAULT nextval('public.widget_reviews_id_seq'::regclass);


--
-- Name: widgets id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widgets ALTER COLUMN id SET DEFAULT nextval('public.widgets_id_seq'::regclass);


--
-- Name: access_package_allotments access_package_allotments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.access_package_allotments
    ADD CONSTRAINT access_package_allotments_pkey PRIMARY KEY (id);


--
-- Name: access_packages access_packages_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.access_packages
    ADD CONSTRAINT access_packages_pkey PRIMARY KEY (id);


--
-- Name: action_text_rich_texts action_text_rich_texts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.action_text_rich_texts
    ADD CONSTRAINT action_text_rich_texts_pkey PRIMARY KEY (id);


--
-- Name: active_admin_comments active_admin_comments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_admin_comments
    ADD CONSTRAINT active_admin_comments_pkey PRIMARY KEY (id);


--
-- Name: active_campaign_records active_campaign_records_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_campaign_records
    ADD CONSTRAINT active_campaign_records_pkey PRIMARY KEY (id);


--
-- Name: active_storage_attachments active_storage_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_attachments
    ADD CONSTRAINT active_storage_attachments_pkey PRIMARY KEY (id);


--
-- Name: active_storage_blobs active_storage_blobs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_blobs
    ADD CONSTRAINT active_storage_blobs_pkey PRIMARY KEY (id);


--
-- Name: active_storage_variant_records active_storage_variant_records_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_variant_records
    ADD CONSTRAINT active_storage_variant_records_pkey PRIMARY KEY (id);


--
-- Name: analytics_companies analytics_companies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_companies
    ADD CONSTRAINT analytics_companies_pkey PRIMARY KEY (id);


--
-- Name: analytics_company_event_feed_exports analytics_company_event_feed_exports_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_exports
    ADD CONSTRAINT analytics_company_event_feed_exports_pkey PRIMARY KEY (id);


--
-- Name: analytics_company_event_feed_notification_logs analytics_company_event_feed_notification_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notification_logs
    ADD CONSTRAINT analytics_company_event_feed_notification_logs_pkey PRIMARY KEY (id);


--
-- Name: analytics_company_event_feed_notifications analytics_company_event_feed_notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notifications
    ADD CONSTRAINT analytics_company_event_feed_notifications_pkey PRIMARY KEY (id);


--
-- Name: analytics_company_event_feeds analytics_company_event_feeds_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feeds
    ADD CONSTRAINT analytics_company_event_feeds_pkey PRIMARY KEY (id);


--
-- Name: analytics_company_providers analytics_company_providers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_providers
    ADD CONSTRAINT analytics_company_providers_pkey PRIMARY KEY (id);


--
-- Name: analytics_event_feeds analytics_event_feeds_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_event_feeds
    ADD CONSTRAINT analytics_event_feeds_pkey PRIMARY KEY (id);


--
-- Name: analytics_events analytics_events_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events
    ADD CONSTRAINT analytics_events_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2024_09 analytics_events_2024_09_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2024_09
    ADD CONSTRAINT analytics_events_2024_09_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2024_10 analytics_events_2024_10_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2024_10
    ADD CONSTRAINT analytics_events_2024_10_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2024_11 analytics_events_2024_11_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2024_11
    ADD CONSTRAINT analytics_events_2024_11_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2024_12 analytics_events_2024_12_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2024_12
    ADD CONSTRAINT analytics_events_2024_12_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_01 analytics_events_2025_01_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_01
    ADD CONSTRAINT analytics_events_2025_01_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_02 analytics_events_2025_02_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_02
    ADD CONSTRAINT analytics_events_2025_02_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_03 analytics_events_2025_03_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_03
    ADD CONSTRAINT analytics_events_2025_03_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_04 analytics_events_2025_04_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_04
    ADD CONSTRAINT analytics_events_2025_04_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_05 analytics_events_2025_05_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_05
    ADD CONSTRAINT analytics_events_2025_05_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_06 analytics_events_2025_06_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_06
    ADD CONSTRAINT analytics_events_2025_06_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_07 analytics_events_2025_07_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_07
    ADD CONSTRAINT analytics_events_2025_07_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_08 analytics_events_2025_08_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_08
    ADD CONSTRAINT analytics_events_2025_08_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_09 analytics_events_2025_09_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_09
    ADD CONSTRAINT analytics_events_2025_09_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_events_2025_10 analytics_events_2025_10_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_events_2025_10
    ADD CONSTRAINT analytics_events_2025_10_pkey PRIMARY KEY (id, "time");


--
-- Name: analytics_industries analytics_industries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_industries
    ADD CONSTRAINT analytics_industries_pkey PRIMARY KEY (id);


--
-- Name: analytics_integrations analytics_integrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_integrations
    ADD CONSTRAINT analytics_integrations_pkey PRIMARY KEY (id);


--
-- Name: analytics_load_industries analytics_load_industries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_load_industries
    ADD CONSTRAINT analytics_load_industries_pkey PRIMARY KEY (id);


--
-- Name: analytics_visits analytics_visits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits
    ADD CONSTRAINT analytics_visits_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2024_09 analytics_visits_2024_09_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2024_09
    ADD CONSTRAINT analytics_visits_2024_09_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2024_10 analytics_visits_2024_10_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2024_10
    ADD CONSTRAINT analytics_visits_2024_10_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2024_11 analytics_visits_2024_11_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2024_11
    ADD CONSTRAINT analytics_visits_2024_11_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2024_12 analytics_visits_2024_12_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2024_12
    ADD CONSTRAINT analytics_visits_2024_12_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_01 analytics_visits_2025_01_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_01
    ADD CONSTRAINT analytics_visits_2025_01_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_02 analytics_visits_2025_02_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_02
    ADD CONSTRAINT analytics_visits_2025_02_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_03 analytics_visits_2025_03_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_03
    ADD CONSTRAINT analytics_visits_2025_03_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_04 analytics_visits_2025_04_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_04
    ADD CONSTRAINT analytics_visits_2025_04_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_05 analytics_visits_2025_05_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_05
    ADD CONSTRAINT analytics_visits_2025_05_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_06 analytics_visits_2025_06_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_06
    ADD CONSTRAINT analytics_visits_2025_06_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_07 analytics_visits_2025_07_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_07
    ADD CONSTRAINT analytics_visits_2025_07_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_08 analytics_visits_2025_08_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_08
    ADD CONSTRAINT analytics_visits_2025_08_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_09 analytics_visits_2025_09_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_09
    ADD CONSTRAINT analytics_visits_2025_09_pkey PRIMARY KEY (id, started_at);


--
-- Name: analytics_visits_2025_10 analytics_visits_2025_10_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_visits_2025_10
    ADD CONSTRAINT analytics_visits_2025_10_pkey PRIMARY KEY (id, started_at);


--
-- Name: api_tokens api_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.api_tokens
    ADD CONSTRAINT api_tokens_pkey PRIMARY KEY (id);


--
-- Name: ar_internal_metadata ar_internal_metadata_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ar_internal_metadata
    ADD CONSTRAINT ar_internal_metadata_pkey PRIMARY KEY (key);


--
-- Name: audits audits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audits
    ADD CONSTRAINT audits_pkey PRIMARY KEY (id);


--
-- Name: authentications authentications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authentications
    ADD CONSTRAINT authentications_pkey PRIMARY KEY (id);


--
-- Name: authhist authhist_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authhist
    ADD CONSTRAINT authhist_pkey PRIMARY KEY (id);


--
-- Name: basics_measure basics_measures_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.basics_measure
    ADD CONSTRAINT basics_measures_pkey PRIMARY KEY (dot_number);


--
-- Name: blocked_ips blocked_ips_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.blocked_ips
    ADD CONSTRAINT blocked_ips_pkey PRIMARY KEY (id);


--
-- Name: bookmarks bookmarks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bookmarks
    ADD CONSTRAINT bookmarks_pkey PRIMARY KEY (id);


--
-- Name: brokerage_cities brokerage_cities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_cities
    ADD CONSTRAINT brokerage_cities_pkey PRIMARY KEY (id);


--
-- Name: brokerage_domains brokerage_domains_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_domains
    ADD CONSTRAINT brokerage_domains_pkey PRIMARY KEY (id);


--
-- Name: brokerage_onboardings brokerage_onboardings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_onboardings
    ADD CONSTRAINT brokerage_onboardings_pkey PRIMARY KEY (id);


--
-- Name: brokerage_profile_assets brokerage_profile_assets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_assets
    ADD CONSTRAINT brokerage_profile_assets_pkey PRIMARY KEY (id);


--
-- Name: brokerage_profile_contacts brokerage_profile_contacts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_contacts
    ADD CONSTRAINT brokerage_profile_contacts_pkey PRIMARY KEY (id);


--
-- Name: brokerage_profile_preferred_lanes brokerage_profile_preferred_lanes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_preferred_lanes
    ADD CONSTRAINT brokerage_profile_preferred_lanes_pkey PRIMARY KEY (id);


--
-- Name: brokerage_profile_users brokerage_profile_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_users
    ADD CONSTRAINT brokerage_profile_users_pkey PRIMARY KEY (id);


--
-- Name: brokerage_profile_widgets brokerage_profile_widgets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_widgets
    ADD CONSTRAINT brokerage_profile_widgets_pkey PRIMARY KEY (id);


--
-- Name: brokerage_profiles brokerage_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profiles
    ADD CONSTRAINT brokerage_profiles_pkey PRIMARY KEY (id);


--
-- Name: brokerage_review_aggregates brokerage_review_aggregates_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_aggregates
    ADD CONSTRAINT brokerage_review_aggregates_pkey PRIMARY KEY (id);


--
-- Name: brokerage_review_replies brokerage_review_replies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_replies
    ADD CONSTRAINT brokerage_review_replies_pkey PRIMARY KEY (id);


--
-- Name: brokerage_review_reports brokerage_review_reports_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_reports
    ADD CONSTRAINT brokerage_review_reports_pkey PRIMARY KEY (id);


--
-- Name: brokerage_review_sentiments brokerage_review_sentiments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_sentiments
    ADD CONSTRAINT brokerage_review_sentiments_pkey PRIMARY KEY (id);


--
-- Name: brokerage_reviews brokerage_reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews
    ADD CONSTRAINT brokerage_reviews_pkey PRIMARY KEY (id);


--
-- Name: brokerage_reviews_review_lanes brokerage_reviews_review_lanes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews_review_lanes
    ADD CONSTRAINT brokerage_reviews_review_lanes_pkey PRIMARY KEY (id);


--
-- Name: brokerages_review_lanes brokerages_review_lanes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_review_lanes
    ADD CONSTRAINT brokerages_review_lanes_pkey PRIMARY KEY (id);


--
-- Name: brokerages_shipment_types brokerages_shipment_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_shipment_types
    ADD CONSTRAINT brokerages_shipment_types_pkey PRIMARY KEY (id);


--
-- Name: brokerages_specialized_services brokerages_specialized_services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_specialized_services
    ADD CONSTRAINT brokerages_specialized_services_pkey PRIMARY KEY (id);


--
-- Name: brokerages_truck_types brokerages_truck_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_truck_types
    ADD CONSTRAINT brokerages_truck_types_pkey PRIMARY KEY (id);


--
-- Name: carrier_availabilities carrier_availabilities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availabilities
    ADD CONSTRAINT carrier_availabilities_pkey PRIMARY KEY (id);


--
-- Name: carrier_availability_destinations carrier_availability_destinations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_destinations
    ADD CONSTRAINT carrier_availability_destinations_pkey PRIMARY KEY (id);


--
-- Name: carrier_availability_origins carrier_availability_origins_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_origins
    ADD CONSTRAINT carrier_availability_origins_pkey PRIMARY KEY (id);


--
-- Name: carrier_availability_unsubscribes carrier_availability_unsubscribes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_unsubscribes
    ADD CONSTRAINT carrier_availability_unsubscribes_pkey PRIMARY KEY (id);


--
-- Name: carrier_cities carrier_cities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_cities
    ADD CONSTRAINT carrier_cities_pkey PRIMARY KEY (id);


--
-- Name: carrier_csv_exports carrier_csv_exports_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_csv_exports
    ADD CONSTRAINT carrier_csv_exports_pkey PRIMARY KEY (id);


--
-- Name: carrier_network_builder_lane_entities carrier_network_builder_lane_entities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_lane_entities
    ADD CONSTRAINT carrier_network_builder_lane_entities_pkey PRIMARY KEY (id);


--
-- Name: carrier_network_builder_lanes carrier_network_builder_lanes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_lanes
    ADD CONSTRAINT carrier_network_builder_lanes_pkey PRIMARY KEY (id);


--
-- Name: carrier_network_builder_messages carrier_network_builder_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_messages
    ADD CONSTRAINT carrier_network_builder_messages_pkey PRIMARY KEY (id);


--
-- Name: carrier_network_builders carrier_network_builders_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builders
    ADD CONSTRAINT carrier_network_builders_pkey PRIMARY KEY (id);


--
-- Name: carrier_operation_states carrier_operation_states_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_operation_states
    ADD CONSTRAINT carrier_operation_states_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_assets carrier_profile_assets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_assets
    ADD CONSTRAINT carrier_profile_assets_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_contacts carrier_profile_contacts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_contacts
    ADD CONSTRAINT carrier_profile_contacts_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_terminals carrier_profile_terminals_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_terminals
    ADD CONSTRAINT carrier_profile_terminals_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_users carrier_profile_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_users
    ADD CONSTRAINT carrier_profile_users_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_website_highlights carrier_profile_website_highlights_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_highlights
    ADD CONSTRAINT carrier_profile_website_highlights_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_website_reviews carrier_profile_website_reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_reviews
    ADD CONSTRAINT carrier_profile_website_reviews_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_website_services carrier_profile_website_services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_services
    ADD CONSTRAINT carrier_profile_website_services_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_websites carrier_profile_websites_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_websites
    ADD CONSTRAINT carrier_profile_websites_pkey PRIMARY KEY (id);


--
-- Name: carrier_profile_widgets carrier_profile_widgets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_widgets
    ADD CONSTRAINT carrier_profile_widgets_pkey PRIMARY KEY (id);


--
-- Name: carrier_profiles carrier_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profiles
    ADD CONSTRAINT carrier_profiles_pkey PRIMARY KEY (id);


--
-- Name: carriers_review_lanes carriers_review_lanes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_review_lanes
    ADD CONSTRAINT carriers_review_lanes_pkey PRIMARY KEY (id);


--
-- Name: carriers_shipment_types carriers_shipment_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_shipment_types
    ADD CONSTRAINT carriers_shipment_types_pkey PRIMARY KEY (id);


--
-- Name: carriers_specialized_services carriers_specialized_services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_specialized_services
    ADD CONSTRAINT carriers_specialized_services_pkey PRIMARY KEY (id);


--
-- Name: carriers_truck_types carriers_truck_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_truck_types
    ADD CONSTRAINT carriers_truck_types_pkey PRIMARY KEY (id);


--
-- Name: census census_dot_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.census
    ADD CONSTRAINT census_dot_number_key UNIQUE (dot_number);


--
-- Name: census census_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.census
    ADD CONSTRAINT census_pkey PRIMARY KEY (id);


--
-- Name: censuses censuses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.censuses
    ADD CONSTRAINT censuses_pkey PRIMARY KEY (id);


--
-- Name: cities_metadata cities_metadata_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_metadata
    ADD CONSTRAINT cities_metadata_pkey PRIMARY KEY (id);


--
-- Name: cities cities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities
    ADD CONSTRAINT cities_pkey PRIMARY KEY (id);


--
-- Name: cities_postal_codes cities_postal_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_postal_codes
    ADD CONSTRAINT cities_postal_codes_pkey PRIMARY KEY (id);


--
-- Name: city_freights city_freights_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_freights
    ADD CONSTRAINT city_freights_pkey PRIMARY KEY (id);


--
-- Name: city_shipment_types city_shipment_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_shipment_types
    ADD CONSTRAINT city_shipment_types_pkey PRIMARY KEY (id);


--
-- Name: city_specialized_services city_specialized_services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_specialized_services
    ADD CONSTRAINT city_specialized_services_pkey PRIMARY KEY (id);


--
-- Name: city_truck_types city_truck_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_truck_types
    ADD CONSTRAINT city_truck_types_pkey PRIMARY KEY (id);


--
-- Name: claim_partners claim_partners_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.claim_partners
    ADD CONSTRAINT claim_partners_pkey PRIMARY KEY (id);


--
-- Name: companies_freights companies_freights_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_freights
    ADD CONSTRAINT companies_freights_pkey PRIMARY KEY (id);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- Name: companies_related_companies companies_related_companies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_related_companies
    ADD CONSTRAINT companies_related_companies_pkey PRIMARY KEY (id);


--
-- Name: company_entity_types company_entity_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_entity_types
    ADD CONSTRAINT company_entity_types_pkey PRIMARY KEY (id);


--
-- Name: company_notes company_notes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_notes
    ADD CONSTRAINT company_notes_pkey PRIMARY KEY (id);


--
-- Name: crash crash_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.crash
    ADD CONSTRAINT crash_pkey PRIMARY KEY (id);


--
-- Name: data_uploads data_uploads_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_uploads
    ADD CONSTRAINT data_uploads_pkey PRIMARY KEY (id);


--
-- Name: developer_account_permissions developer_account_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_account_permissions
    ADD CONSTRAINT developer_account_permissions_pkey PRIMARY KEY (id);


--
-- Name: developer_account_users developer_account_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_account_users
    ADD CONSTRAINT developer_account_users_pkey PRIMARY KEY (id);


--
-- Name: developer_accounts developer_accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_accounts
    ADD CONSTRAINT developer_accounts_pkey PRIMARY KEY (id);


--
-- Name: domain_certificates domain_certificates_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.domain_certificates
    ADD CONSTRAINT domain_certificates_pkey PRIMARY KEY (id);


--
-- Name: driver_carrier_reviews_aggregates driver_carrier_reviews_aggregates_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews_aggregates
    ADD CONSTRAINT driver_carrier_reviews_aggregates_pkey PRIMARY KEY (id);


--
-- Name: driver_carrier_reviews driver_carrier_reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews
    ADD CONSTRAINT driver_carrier_reviews_pkey PRIMARY KEY (id);


--
-- Name: driver_employment_experiences driver_employment_experiences_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_employment_experiences
    ADD CONSTRAINT driver_employment_experiences_pkey PRIMARY KEY (id);


--
-- Name: driver_interview_availabilities driver_interview_availabilities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_interview_availabilities
    ADD CONSTRAINT driver_interview_availabilities_pkey PRIMARY KEY (id);


--
-- Name: driver_job_applications driver_job_applications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_job_applications
    ADD CONSTRAINT driver_job_applications_pkey PRIMARY KEY (id);


--
-- Name: driver_jobs driver_jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_jobs
    ADD CONSTRAINT driver_jobs_pkey PRIMARY KEY (id);


--
-- Name: email_domains email_domains_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.email_domains
    ADD CONSTRAINT email_domains_pkey PRIMARY KEY (id);


--
-- Name: email_feedbacks email_feedbacks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.email_feedbacks
    ADD CONSTRAINT email_feedbacks_pkey PRIMARY KEY (id);


--
-- Name: filer filer_insurance_company_unique; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.filer
    ADD CONSTRAINT filer_insurance_company_unique UNIQUE (insurance_company);


--
-- Name: filer filer_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.filer
    ADD CONSTRAINT filer_pkey PRIMARY KEY (id);


--
-- Name: freights freights_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.freights
    ADD CONSTRAINT freights_pkey PRIMARY KEY (id);


--
-- Name: friendly_id_slugs friendly_id_slugs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.friendly_id_slugs
    ADD CONSTRAINT friendly_id_slugs_pkey PRIMARY KEY (id);


--
-- Name: incoming_webhook_events incoming_webhook_events_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.incoming_webhook_events
    ADD CONSTRAINT incoming_webhook_events_pkey PRIMARY KEY (id);


--
-- Name: inspections inspections_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inspections
    ADD CONSTRAINT inspections_pkey PRIMARY KEY (id);


--
-- Name: insurance insurance_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.insurance
    ADD CONSTRAINT insurance_pkey PRIMARY KEY (id);


--
-- Name: lists lists_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.lists
    ADD CONSTRAINT lists_pkey PRIMARY KEY (id);


--
-- Name: one_off_tasks one_off_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.one_off_tasks
    ADD CONSTRAINT one_off_tasks_pkey PRIMARY KEY (id);


--
-- Name: operating_authorities operating_authorities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.operating_authorities
    ADD CONSTRAINT operating_authorities_pkey PRIMARY KEY (id);


--
-- Name: page_views page_views_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.page_views
    ADD CONSTRAINT page_views_pkey PRIMARY KEY (id);


--
-- Name: persona_verifications persona_verifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.persona_verifications
    ADD CONSTRAINT persona_verifications_pkey PRIMARY KEY (id);


--
-- Name: personas personas_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personas
    ADD CONSTRAINT personas_pkey PRIMARY KEY (id);


--
-- Name: postal_codes postal_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.postal_codes
    ADD CONSTRAINT postal_codes_pkey PRIMARY KEY (id);


--
-- Name: preferred_lanes preferred_lanes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.preferred_lanes
    ADD CONSTRAINT preferred_lanes_pkey PRIMARY KEY (id);


--
-- Name: recently_vieweds recently_vieweds_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.recently_vieweds
    ADD CONSTRAINT recently_vieweds_pkey PRIMARY KEY (id);


--
-- Name: replies replies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.replies
    ADD CONSTRAINT replies_pkey PRIMARY KEY (id);


--
-- Name: reports reports_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reports
    ADD CONSTRAINT reports_pkey PRIMARY KEY (id);


--
-- Name: request_for_proposals request_for_proposals_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT request_for_proposals_pkey PRIMARY KEY (id);


--
-- Name: review_lanes review_lanes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_lanes
    ADD CONSTRAINT review_lanes_pkey PRIMARY KEY (id);


--
-- Name: review_sentiments review_sentiments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_sentiments
    ADD CONSTRAINT review_sentiments_pkey PRIMARY KEY (id);


--
-- Name: reviews_aggregates reviews_aggregates_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews_aggregates
    ADD CONSTRAINT reviews_aggregates_pkey PRIMARY KEY (id);


--
-- Name: reviews reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_pkey PRIMARY KEY (id);


--
-- Name: role_actions role_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_actions
    ADD CONSTRAINT role_actions_pkey PRIMARY KEY (id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: rollups rollups_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.rollups
    ADD CONSTRAINT rollups_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sentiment_personas sentiment_personas_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sentiment_personas
    ADD CONSTRAINT sentiment_personas_pkey PRIMARY KEY (id);


--
-- Name: sentiments sentiments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sentiments
    ADD CONSTRAINT sentiments_pkey PRIMARY KEY (id);


--
-- Name: shipment_types shipment_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipment_types
    ADD CONSTRAINT shipment_types_pkey PRIMARY KEY (id);


--
-- Name: specialized_services specialized_services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.specialized_services
    ADD CONSTRAINT specialized_services_pkey PRIMARY KEY (id);


--
-- Name: subscriptions subscriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT subscriptions_pkey PRIMARY KEY (id);


--
-- Name: truck_types truck_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.truck_types
    ADD CONSTRAINT truck_types_pkey PRIMARY KEY (id);


--
-- Name: user_emails user_emails_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_emails
    ADD CONSTRAINT user_emails_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: utm_params utm_params_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.utm_params
    ADD CONSTRAINT utm_params_pkey PRIMARY KEY (id);


--
-- Name: versions versions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.versions
    ADD CONSTRAINT versions_pkey PRIMARY KEY (id);


--
-- Name: violation violation_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.violation
    ADD CONSTRAINT violation_pkey PRIMARY KEY (id);


--
-- Name: webhooks_events webhooks_events_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.webhooks_events
    ADD CONSTRAINT webhooks_events_pkey PRIMARY KEY (id);


--
-- Name: webhooks_subscriptions webhooks_subscriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.webhooks_subscriptions
    ADD CONSTRAINT webhooks_subscriptions_pkey PRIMARY KEY (id);


--
-- Name: widget_brokerage_reviews widget_brokerage_reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_brokerage_reviews
    ADD CONSTRAINT widget_brokerage_reviews_pkey PRIMARY KEY (id);


--
-- Name: widget_reviews widget_reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_reviews
    ADD CONSTRAINT widget_reviews_pkey PRIMARY KEY (id);


--
-- Name: widgets widgets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widgets
    ADD CONSTRAINT widgets_pkey PRIMARY KEY (id);


--
-- Name: idx_on_company_id_company_entity_type_2e8c16e15e; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_company_id_company_entity_type_2e8c16e15e ON ONLY public.analytics_aggregate_shipper_events USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx1; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx1 ON public.analytics_aggregate_shipper_events_2025_03 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx2; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx2 ON public.analytics_aggregate_shipper_events_2025_04 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx3; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx3 ON public.analytics_aggregate_shipper_events_2025_05 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx4; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx4 ON public.analytics_aggregate_shipper_events_2025_06 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx5; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx5 ON public.analytics_aggregate_shipper_events_2025_07 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx6; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx6 ON public.analytics_aggregate_shipper_events_2025_08 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx7; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx7 ON public.analytics_aggregate_shipper_events_2025_09 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx8; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_ty_idx8 ON public.analytics_aggregate_shipper_events_2025_10 USING btree (company_id, company_entity_type);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_typ_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_e_company_id_company_entity_typ_idx ON public.analytics_aggregate_shipper_events_2025_02 USING btree (company_id, company_entity_type);


--
-- Name: idx_on_id_interval_date_01e1f80846; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_id_interval_date_01e1f80846 ON ONLY public.analytics_aggregate_shipper_events USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_02_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_02_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_02 USING btree (id, interval_date);


--
-- Name: index_analytics_aggregate_shipper_events_on_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_aggregate_shipper_events_on_type ON ONLY public.analytics_aggregate_shipper_events USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_02_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_02_type_idx ON public.analytics_aggregate_shipper_events_2025_02 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_03_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_03_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_03 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_03_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_03_type_idx ON public.analytics_aggregate_shipper_events_2025_03 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_04_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_04_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_04 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_04_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_04_type_idx ON public.analytics_aggregate_shipper_events_2025_04 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_05_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_05_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_05 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_05_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_05_type_idx ON public.analytics_aggregate_shipper_events_2025_05 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_06_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_06_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_06 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_06_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_06_type_idx ON public.analytics_aggregate_shipper_events_2025_06 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_07_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_07_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_07 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_07_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_07_type_idx ON public.analytics_aggregate_shipper_events_2025_07 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_08_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_08_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_08 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_08_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_08_type_idx ON public.analytics_aggregate_shipper_events_2025_08 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_09_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_09_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_09 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_09_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_09_type_idx ON public.analytics_aggregate_shipper_events_2025_09 USING btree (type);


--
-- Name: analytics_aggregate_shipper_events_2025_10_id_interval_date_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_aggregate_shipper_events_2025_10_id_interval_date_idx ON public.analytics_aggregate_shipper_events_2025_10 USING btree (id, interval_date);


--
-- Name: analytics_aggregate_shipper_events_2025_10_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_2025_10_type_idx ON public.analytics_aggregate_shipper_events_2025_10 USING btree (type);


--
-- Name: idx_on_analytics_company_id_3d7118b2d8; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_analytics_company_id_3d7118b2d8 ON ONLY public.analytics_aggregate_shipper_events USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_202_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_202_analytics_company_id_idx ON public.analytics_aggregate_shipper_events_2025_02 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx1; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx1 ON public.analytics_aggregate_shipper_events_2025_03 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx2; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx2 ON public.analytics_aggregate_shipper_events_2025_04 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx3; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx3 ON public.analytics_aggregate_shipper_events_2025_05 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx4; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx4 ON public.analytics_aggregate_shipper_events_2025_06 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx5; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx5 ON public.analytics_aggregate_shipper_events_2025_07 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx6; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx6 ON public.analytics_aggregate_shipper_events_2025_08 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx7; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx7 ON public.analytics_aggregate_shipper_events_2025_09 USING btree (analytics_company_id);


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx8; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_aggregate_shipper_events_20_analytics_company_id_idx8 ON public.analytics_aggregate_shipper_events_2025_10 USING btree (analytics_company_id);


--
-- Name: index_analytics_events_on_id_and_time; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_events_on_id_and_time ON ONLY public.analytics_events USING btree (id, "time");


--
-- Name: analytics_events_2024_09_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2024_09_id_time_idx ON public.analytics_events_2024_09 USING btree (id, "time");


--
-- Name: index_analytics_events_on_name_and_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_events_on_name_and_time ON ONLY public.analytics_events USING btree (name, "time");


--
-- Name: analytics_events_2024_09_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_09_name_time_idx ON public.analytics_events_2024_09 USING btree (name, "time");


--
-- Name: index_analytics_events_on_properties; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_events_on_properties ON ONLY public.analytics_events USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2024_09_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_09_properties_idx ON public.analytics_events_2024_09 USING gin (properties jsonb_path_ops);


--
-- Name: index_analytics_events_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_events_on_user_id ON ONLY public.analytics_events USING btree (user_id);


--
-- Name: analytics_events_2024_09_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_09_user_id_idx ON public.analytics_events_2024_09 USING btree (user_id);


--
-- Name: index_analytics_events_on_visit_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_events_on_visit_id ON ONLY public.analytics_events USING btree (visit_id);


--
-- Name: analytics_events_2024_09_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_09_visit_id_idx ON public.analytics_events_2024_09 USING btree (visit_id);


--
-- Name: analytics_events_2024_10_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2024_10_id_time_idx ON public.analytics_events_2024_10 USING btree (id, "time");


--
-- Name: analytics_events_2024_10_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_10_name_time_idx ON public.analytics_events_2024_10 USING btree (name, "time");


--
-- Name: analytics_events_2024_10_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_10_properties_idx ON public.analytics_events_2024_10 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2024_10_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_10_user_id_idx ON public.analytics_events_2024_10 USING btree (user_id);


--
-- Name: analytics_events_2024_10_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_10_visit_id_idx ON public.analytics_events_2024_10 USING btree (visit_id);


--
-- Name: analytics_events_2024_11_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2024_11_id_time_idx ON public.analytics_events_2024_11 USING btree (id, "time");


--
-- Name: analytics_events_2024_11_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_11_name_time_idx ON public.analytics_events_2024_11 USING btree (name, "time");


--
-- Name: analytics_events_2024_11_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_11_properties_idx ON public.analytics_events_2024_11 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2024_11_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_11_user_id_idx ON public.analytics_events_2024_11 USING btree (user_id);


--
-- Name: analytics_events_2024_11_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_11_visit_id_idx ON public.analytics_events_2024_11 USING btree (visit_id);


--
-- Name: analytics_events_2024_12_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2024_12_id_time_idx ON public.analytics_events_2024_12 USING btree (id, "time");


--
-- Name: analytics_events_2024_12_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_12_name_time_idx ON public.analytics_events_2024_12 USING btree (name, "time");


--
-- Name: analytics_events_2024_12_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_12_properties_idx ON public.analytics_events_2024_12 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2024_12_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_12_user_id_idx ON public.analytics_events_2024_12 USING btree (user_id);


--
-- Name: analytics_events_2024_12_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2024_12_visit_id_idx ON public.analytics_events_2024_12 USING btree (visit_id);


--
-- Name: analytics_events_2025_01_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_01_id_time_idx ON public.analytics_events_2025_01 USING btree (id, "time");


--
-- Name: analytics_events_2025_01_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_01_name_time_idx ON public.analytics_events_2025_01 USING btree (name, "time");


--
-- Name: analytics_events_2025_01_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_01_properties_idx ON public.analytics_events_2025_01 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_01_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_01_user_id_idx ON public.analytics_events_2025_01 USING btree (user_id);


--
-- Name: analytics_events_2025_01_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_01_visit_id_idx ON public.analytics_events_2025_01 USING btree (visit_id);


--
-- Name: analytics_events_2025_02_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_02_id_time_idx ON public.analytics_events_2025_02 USING btree (id, "time");


--
-- Name: analytics_events_2025_02_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_02_name_time_idx ON public.analytics_events_2025_02 USING btree (name, "time");


--
-- Name: analytics_events_2025_02_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_02_properties_idx ON public.analytics_events_2025_02 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_02_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_02_user_id_idx ON public.analytics_events_2025_02 USING btree (user_id);


--
-- Name: analytics_events_2025_02_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_02_visit_id_idx ON public.analytics_events_2025_02 USING btree (visit_id);


--
-- Name: analytics_events_2025_03_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_03_id_time_idx ON public.analytics_events_2025_03 USING btree (id, "time");


--
-- Name: analytics_events_2025_03_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_03_name_time_idx ON public.analytics_events_2025_03 USING btree (name, "time");


--
-- Name: analytics_events_2025_03_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_03_properties_idx ON public.analytics_events_2025_03 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_03_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_03_user_id_idx ON public.analytics_events_2025_03 USING btree (user_id);


--
-- Name: analytics_events_2025_03_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_03_visit_id_idx ON public.analytics_events_2025_03 USING btree (visit_id);


--
-- Name: analytics_events_2025_04_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_04_id_time_idx ON public.analytics_events_2025_04 USING btree (id, "time");


--
-- Name: analytics_events_2025_04_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_04_name_time_idx ON public.analytics_events_2025_04 USING btree (name, "time");


--
-- Name: analytics_events_2025_04_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_04_properties_idx ON public.analytics_events_2025_04 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_04_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_04_user_id_idx ON public.analytics_events_2025_04 USING btree (user_id);


--
-- Name: analytics_events_2025_04_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_04_visit_id_idx ON public.analytics_events_2025_04 USING btree (visit_id);


--
-- Name: analytics_events_2025_05_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_05_id_time_idx ON public.analytics_events_2025_05 USING btree (id, "time");


--
-- Name: analytics_events_2025_05_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_05_name_time_idx ON public.analytics_events_2025_05 USING btree (name, "time");


--
-- Name: analytics_events_2025_05_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_05_properties_idx ON public.analytics_events_2025_05 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_05_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_05_user_id_idx ON public.analytics_events_2025_05 USING btree (user_id);


--
-- Name: analytics_events_2025_05_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_05_visit_id_idx ON public.analytics_events_2025_05 USING btree (visit_id);


--
-- Name: analytics_events_2025_06_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_06_id_time_idx ON public.analytics_events_2025_06 USING btree (id, "time");


--
-- Name: analytics_events_2025_06_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_06_name_time_idx ON public.analytics_events_2025_06 USING btree (name, "time");


--
-- Name: analytics_events_2025_06_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_06_properties_idx ON public.analytics_events_2025_06 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_06_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_06_user_id_idx ON public.analytics_events_2025_06 USING btree (user_id);


--
-- Name: analytics_events_2025_06_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_06_visit_id_idx ON public.analytics_events_2025_06 USING btree (visit_id);


--
-- Name: analytics_events_2025_07_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_07_id_time_idx ON public.analytics_events_2025_07 USING btree (id, "time");


--
-- Name: analytics_events_2025_07_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_07_name_time_idx ON public.analytics_events_2025_07 USING btree (name, "time");


--
-- Name: analytics_events_2025_07_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_07_properties_idx ON public.analytics_events_2025_07 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_07_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_07_user_id_idx ON public.analytics_events_2025_07 USING btree (user_id);


--
-- Name: analytics_events_2025_07_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_07_visit_id_idx ON public.analytics_events_2025_07 USING btree (visit_id);


--
-- Name: analytics_events_2025_08_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_08_id_time_idx ON public.analytics_events_2025_08 USING btree (id, "time");


--
-- Name: analytics_events_2025_08_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_08_name_time_idx ON public.analytics_events_2025_08 USING btree (name, "time");


--
-- Name: analytics_events_2025_08_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_08_properties_idx ON public.analytics_events_2025_08 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_08_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_08_user_id_idx ON public.analytics_events_2025_08 USING btree (user_id);


--
-- Name: analytics_events_2025_08_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_08_visit_id_idx ON public.analytics_events_2025_08 USING btree (visit_id);


--
-- Name: analytics_events_2025_09_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_09_id_time_idx ON public.analytics_events_2025_09 USING btree (id, "time");


--
-- Name: analytics_events_2025_09_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_09_name_time_idx ON public.analytics_events_2025_09 USING btree (name, "time");


--
-- Name: analytics_events_2025_09_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_09_properties_idx ON public.analytics_events_2025_09 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_09_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_09_user_id_idx ON public.analytics_events_2025_09 USING btree (user_id);


--
-- Name: analytics_events_2025_09_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_09_visit_id_idx ON public.analytics_events_2025_09 USING btree (visit_id);


--
-- Name: analytics_events_2025_10_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_events_2025_10_id_time_idx ON public.analytics_events_2025_10 USING btree (id, "time");


--
-- Name: analytics_events_2025_10_name_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_10_name_time_idx ON public.analytics_events_2025_10 USING btree (name, "time");


--
-- Name: analytics_events_2025_10_properties_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_10_properties_idx ON public.analytics_events_2025_10 USING gin (properties jsonb_path_ops);


--
-- Name: analytics_events_2025_10_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_10_user_id_idx ON public.analytics_events_2025_10 USING btree (user_id);


--
-- Name: analytics_events_2025_10_visit_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_events_2025_10_visit_id_idx ON public.analytics_events_2025_10 USING btree (visit_id);


--
-- Name: index_analytics_shipper_events_on_analytics_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_shipper_events_on_analytics_company_id ON ONLY public.analytics_shipper_events USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2024_09_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_09_analytics_company_id_idx ON public.analytics_shipper_events_2024_09 USING btree (analytics_company_id);


--
-- Name: index_analytics_shipper_events_on_event_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_shipper_events_on_event_id ON ONLY public.analytics_shipper_events USING btree (event_id);


--
-- Name: analytics_shipper_events_2024_09_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_09_event_id_idx ON public.analytics_shipper_events_2024_09 USING btree (event_id);


--
-- Name: index_analytics_shipper_events_on_id_and_time; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_shipper_events_on_id_and_time ON ONLY public.analytics_shipper_events USING btree (id, "time");


--
-- Name: analytics_shipper_events_2024_09_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2024_09_id_time_idx ON public.analytics_shipper_events_2024_09 USING btree (id, "time");


--
-- Name: index_analytics_shipper_events_on_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_shipper_events_on_type ON ONLY public.analytics_shipper_events USING btree (type);


--
-- Name: analytics_shipper_events_2024_09_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_09_type_idx ON public.analytics_shipper_events_2024_09 USING btree (type);


--
-- Name: analytics_shipper_events_2024_10_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_10_analytics_company_id_idx ON public.analytics_shipper_events_2024_10 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2024_10_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_10_event_id_idx ON public.analytics_shipper_events_2024_10 USING btree (event_id);


--
-- Name: analytics_shipper_events_2024_10_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2024_10_id_time_idx ON public.analytics_shipper_events_2024_10 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2024_10_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_10_type_idx ON public.analytics_shipper_events_2024_10 USING btree (type);


--
-- Name: analytics_shipper_events_2024_11_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_11_analytics_company_id_idx ON public.analytics_shipper_events_2024_11 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2024_11_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_11_event_id_idx ON public.analytics_shipper_events_2024_11 USING btree (event_id);


--
-- Name: analytics_shipper_events_2024_11_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2024_11_id_time_idx ON public.analytics_shipper_events_2024_11 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2024_11_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_11_type_idx ON public.analytics_shipper_events_2024_11 USING btree (type);


--
-- Name: analytics_shipper_events_2024_12_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_12_analytics_company_id_idx ON public.analytics_shipper_events_2024_12 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2024_12_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_12_event_id_idx ON public.analytics_shipper_events_2024_12 USING btree (event_id);


--
-- Name: analytics_shipper_events_2024_12_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2024_12_id_time_idx ON public.analytics_shipper_events_2024_12 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2024_12_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_12_type_idx ON public.analytics_shipper_events_2024_12 USING btree (type);


--
-- Name: idx_on_company_id_company_entity_type_44355d96dd; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_company_id_company_entity_type_44355d96dd ON ONLY public.analytics_shipper_events USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx2; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_company_id_company_entity_ty_idx2 ON public.analytics_shipper_events_2024_09 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx3; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_company_id_company_entity_ty_idx3 ON public.analytics_shipper_events_2024_10 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx4; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_company_id_company_entity_ty_idx4 ON public.analytics_shipper_events_2024_11 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx5; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2024_company_id_company_entity_ty_idx5 ON public.analytics_shipper_events_2024_12 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_01_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_01_analytics_company_id_idx ON public.analytics_shipper_events_2025_01 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_01_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_01_event_id_idx ON public.analytics_shipper_events_2025_01 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_01_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_01_id_time_idx ON public.analytics_shipper_events_2025_01 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_01_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_01_type_idx ON public.analytics_shipper_events_2025_01 USING btree (type);


--
-- Name: analytics_shipper_events_2025_02_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_02_analytics_company_id_idx ON public.analytics_shipper_events_2025_02 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_02_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_02_event_id_idx ON public.analytics_shipper_events_2025_02 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_02_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_02_id_time_idx ON public.analytics_shipper_events_2025_02 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_02_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_02_type_idx ON public.analytics_shipper_events_2025_02 USING btree (type);


--
-- Name: analytics_shipper_events_2025_03_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_03_analytics_company_id_idx ON public.analytics_shipper_events_2025_03 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_03_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_03_event_id_idx ON public.analytics_shipper_events_2025_03 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_03_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_03_id_time_idx ON public.analytics_shipper_events_2025_03 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_03_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_03_type_idx ON public.analytics_shipper_events_2025_03 USING btree (type);


--
-- Name: analytics_shipper_events_2025_04_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_04_analytics_company_id_idx ON public.analytics_shipper_events_2025_04 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_04_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_04_event_id_idx ON public.analytics_shipper_events_2025_04 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_04_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_04_id_time_idx ON public.analytics_shipper_events_2025_04 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_04_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_04_type_idx ON public.analytics_shipper_events_2025_04 USING btree (type);


--
-- Name: analytics_shipper_events_2025_05_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_05_analytics_company_id_idx ON public.analytics_shipper_events_2025_05 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_05_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_05_event_id_idx ON public.analytics_shipper_events_2025_05 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_05_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_05_id_time_idx ON public.analytics_shipper_events_2025_05 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_05_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_05_type_idx ON public.analytics_shipper_events_2025_05 USING btree (type);


--
-- Name: analytics_shipper_events_2025_06_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_06_analytics_company_id_idx ON public.analytics_shipper_events_2025_06 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_06_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_06_event_id_idx ON public.analytics_shipper_events_2025_06 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_06_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_06_id_time_idx ON public.analytics_shipper_events_2025_06 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_06_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_06_type_idx ON public.analytics_shipper_events_2025_06 USING btree (type);


--
-- Name: analytics_shipper_events_2025_07_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_07_analytics_company_id_idx ON public.analytics_shipper_events_2025_07 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_07_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_07_event_id_idx ON public.analytics_shipper_events_2025_07 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_07_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_07_id_time_idx ON public.analytics_shipper_events_2025_07 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_07_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_07_type_idx ON public.analytics_shipper_events_2025_07 USING btree (type);


--
-- Name: analytics_shipper_events_2025_08_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_08_analytics_company_id_idx ON public.analytics_shipper_events_2025_08 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_08_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_08_event_id_idx ON public.analytics_shipper_events_2025_08 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_08_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_08_id_time_idx ON public.analytics_shipper_events_2025_08 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_08_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_08_type_idx ON public.analytics_shipper_events_2025_08 USING btree (type);


--
-- Name: analytics_shipper_events_2025_09_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_09_analytics_company_id_idx ON public.analytics_shipper_events_2025_09 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_09_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_09_event_id_idx ON public.analytics_shipper_events_2025_09 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_09_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_09_id_time_idx ON public.analytics_shipper_events_2025_09 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_09_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_09_type_idx ON public.analytics_shipper_events_2025_09 USING btree (type);


--
-- Name: analytics_shipper_events_2025_10_analytics_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_10_analytics_company_id_idx ON public.analytics_shipper_events_2025_10 USING btree (analytics_company_id);


--
-- Name: analytics_shipper_events_2025_10_event_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_10_event_id_idx ON public.analytics_shipper_events_2025_10 USING btree (event_id);


--
-- Name: analytics_shipper_events_2025_10_id_time_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_shipper_events_2025_10_id_time_idx ON public.analytics_shipper_events_2025_10 USING btree (id, "time");


--
-- Name: analytics_shipper_events_2025_10_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_10_type_idx ON public.analytics_shipper_events_2025_10 USING btree (type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx1; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx1 ON public.analytics_shipper_events_2025_03 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx2; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx2 ON public.analytics_shipper_events_2025_04 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx3; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx3 ON public.analytics_shipper_events_2025_05 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx4; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx4 ON public.analytics_shipper_events_2025_06 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx5; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx5 ON public.analytics_shipper_events_2025_07 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx6; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx6 ON public.analytics_shipper_events_2025_08 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx7; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx7 ON public.analytics_shipper_events_2025_09 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx8; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx8 ON public.analytics_shipper_events_2025_01 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx9; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_ty_idx9 ON public.analytics_shipper_events_2025_10 USING btree (company_id, company_entity_type);


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_typ_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_shipper_events_2025_company_id_company_entity_typ_idx ON public.analytics_shipper_events_2025_02 USING btree (company_id, company_entity_type);


--
-- Name: index_analytics_visits_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_visits_on_company_id ON ONLY public.analytics_visits USING btree (company_id);


--
-- Name: analytics_visits_2024_09_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_09_company_id_idx ON public.analytics_visits_2024_09 USING btree (company_id);


--
-- Name: index_analytics_visits_on_id_and_started_at; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_visits_on_id_and_started_at ON ONLY public.analytics_visits USING btree (id, started_at);


--
-- Name: analytics_visits_2024_09_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_09_id_started_at_idx ON public.analytics_visits_2024_09 USING btree (id, started_at);


--
-- Name: index_analytics_visits_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_visits_on_user_id ON ONLY public.analytics_visits USING btree (user_id);


--
-- Name: analytics_visits_2024_09_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_09_user_id_idx ON public.analytics_visits_2024_09 USING btree (user_id);


--
-- Name: index_analytics_visits_on_visit_token_and_started_at; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_visits_on_visit_token_and_started_at ON ONLY public.analytics_visits USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2024_09_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_09_visit_token_started_at_idx ON public.analytics_visits_2024_09 USING btree (visit_token, started_at);


--
-- Name: index_analytics_visits_on_visitor_token_and_started_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_visits_on_visitor_token_and_started_at ON ONLY public.analytics_visits USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2024_09_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_09_visitor_token_started_at_idx ON public.analytics_visits_2024_09 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2024_10_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_10_company_id_idx ON public.analytics_visits_2024_10 USING btree (company_id);


--
-- Name: analytics_visits_2024_10_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_10_id_started_at_idx ON public.analytics_visits_2024_10 USING btree (id, started_at);


--
-- Name: analytics_visits_2024_10_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_10_user_id_idx ON public.analytics_visits_2024_10 USING btree (user_id);


--
-- Name: analytics_visits_2024_10_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_10_visit_token_started_at_idx ON public.analytics_visits_2024_10 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2024_10_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_10_visitor_token_started_at_idx ON public.analytics_visits_2024_10 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2024_11_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_11_company_id_idx ON public.analytics_visits_2024_11 USING btree (company_id);


--
-- Name: analytics_visits_2024_11_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_11_id_started_at_idx ON public.analytics_visits_2024_11 USING btree (id, started_at);


--
-- Name: analytics_visits_2024_11_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_11_user_id_idx ON public.analytics_visits_2024_11 USING btree (user_id);


--
-- Name: analytics_visits_2024_11_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_11_visit_token_started_at_idx ON public.analytics_visits_2024_11 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2024_11_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_11_visitor_token_started_at_idx ON public.analytics_visits_2024_11 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2024_12_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_12_company_id_idx ON public.analytics_visits_2024_12 USING btree (company_id);


--
-- Name: analytics_visits_2024_12_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_12_id_started_at_idx ON public.analytics_visits_2024_12 USING btree (id, started_at);


--
-- Name: analytics_visits_2024_12_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_12_user_id_idx ON public.analytics_visits_2024_12 USING btree (user_id);


--
-- Name: analytics_visits_2024_12_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2024_12_visit_token_started_at_idx ON public.analytics_visits_2024_12 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2024_12_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2024_12_visitor_token_started_at_idx ON public.analytics_visits_2024_12 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_01_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_01_company_id_idx ON public.analytics_visits_2025_01 USING btree (company_id);


--
-- Name: analytics_visits_2025_01_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_01_id_started_at_idx ON public.analytics_visits_2025_01 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_01_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_01_user_id_idx ON public.analytics_visits_2025_01 USING btree (user_id);


--
-- Name: analytics_visits_2025_01_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_01_visit_token_started_at_idx ON public.analytics_visits_2025_01 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_01_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_01_visitor_token_started_at_idx ON public.analytics_visits_2025_01 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_02_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_02_company_id_idx ON public.analytics_visits_2025_02 USING btree (company_id);


--
-- Name: analytics_visits_2025_02_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_02_id_started_at_idx ON public.analytics_visits_2025_02 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_02_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_02_user_id_idx ON public.analytics_visits_2025_02 USING btree (user_id);


--
-- Name: analytics_visits_2025_02_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_02_visit_token_started_at_idx ON public.analytics_visits_2025_02 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_02_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_02_visitor_token_started_at_idx ON public.analytics_visits_2025_02 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_03_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_03_company_id_idx ON public.analytics_visits_2025_03 USING btree (company_id);


--
-- Name: analytics_visits_2025_03_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_03_id_started_at_idx ON public.analytics_visits_2025_03 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_03_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_03_user_id_idx ON public.analytics_visits_2025_03 USING btree (user_id);


--
-- Name: analytics_visits_2025_03_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_03_visit_token_started_at_idx ON public.analytics_visits_2025_03 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_03_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_03_visitor_token_started_at_idx ON public.analytics_visits_2025_03 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_04_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_04_company_id_idx ON public.analytics_visits_2025_04 USING btree (company_id);


--
-- Name: analytics_visits_2025_04_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_04_id_started_at_idx ON public.analytics_visits_2025_04 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_04_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_04_user_id_idx ON public.analytics_visits_2025_04 USING btree (user_id);


--
-- Name: analytics_visits_2025_04_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_04_visit_token_started_at_idx ON public.analytics_visits_2025_04 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_04_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_04_visitor_token_started_at_idx ON public.analytics_visits_2025_04 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_05_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_05_company_id_idx ON public.analytics_visits_2025_05 USING btree (company_id);


--
-- Name: analytics_visits_2025_05_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_05_id_started_at_idx ON public.analytics_visits_2025_05 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_05_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_05_user_id_idx ON public.analytics_visits_2025_05 USING btree (user_id);


--
-- Name: analytics_visits_2025_05_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_05_visit_token_started_at_idx ON public.analytics_visits_2025_05 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_05_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_05_visitor_token_started_at_idx ON public.analytics_visits_2025_05 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_06_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_06_company_id_idx ON public.analytics_visits_2025_06 USING btree (company_id);


--
-- Name: analytics_visits_2025_06_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_06_id_started_at_idx ON public.analytics_visits_2025_06 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_06_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_06_user_id_idx ON public.analytics_visits_2025_06 USING btree (user_id);


--
-- Name: analytics_visits_2025_06_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_06_visit_token_started_at_idx ON public.analytics_visits_2025_06 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_06_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_06_visitor_token_started_at_idx ON public.analytics_visits_2025_06 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_07_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_07_company_id_idx ON public.analytics_visits_2025_07 USING btree (company_id);


--
-- Name: analytics_visits_2025_07_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_07_id_started_at_idx ON public.analytics_visits_2025_07 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_07_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_07_user_id_idx ON public.analytics_visits_2025_07 USING btree (user_id);


--
-- Name: analytics_visits_2025_07_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_07_visit_token_started_at_idx ON public.analytics_visits_2025_07 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_07_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_07_visitor_token_started_at_idx ON public.analytics_visits_2025_07 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_08_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_08_company_id_idx ON public.analytics_visits_2025_08 USING btree (company_id);


--
-- Name: analytics_visits_2025_08_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_08_id_started_at_idx ON public.analytics_visits_2025_08 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_08_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_08_user_id_idx ON public.analytics_visits_2025_08 USING btree (user_id);


--
-- Name: analytics_visits_2025_08_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_08_visit_token_started_at_idx ON public.analytics_visits_2025_08 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_08_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_08_visitor_token_started_at_idx ON public.analytics_visits_2025_08 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_09_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_09_company_id_idx ON public.analytics_visits_2025_09 USING btree (company_id);


--
-- Name: analytics_visits_2025_09_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_09_id_started_at_idx ON public.analytics_visits_2025_09 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_09_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_09_user_id_idx ON public.analytics_visits_2025_09 USING btree (user_id);


--
-- Name: analytics_visits_2025_09_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_09_visit_token_started_at_idx ON public.analytics_visits_2025_09 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_09_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_09_visitor_token_started_at_idx ON public.analytics_visits_2025_09 USING btree (visitor_token, started_at);


--
-- Name: analytics_visits_2025_10_company_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_10_company_id_idx ON public.analytics_visits_2025_10 USING btree (company_id);


--
-- Name: analytics_visits_2025_10_id_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_10_id_started_at_idx ON public.analytics_visits_2025_10 USING btree (id, started_at);


--
-- Name: analytics_visits_2025_10_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_10_user_id_idx ON public.analytics_visits_2025_10 USING btree (user_id);


--
-- Name: analytics_visits_2025_10_visit_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX analytics_visits_2025_10_visit_token_started_at_idx ON public.analytics_visits_2025_10 USING btree (visit_token, started_at);


--
-- Name: analytics_visits_2025_10_visitor_token_started_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX analytics_visits_2025_10_visitor_token_started_at_idx ON public.analytics_visits_2025_10 USING btree (visitor_token, started_at);


--
-- Name: idx_on_analytics_company_id_c8276dcbe3; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_analytics_company_id_c8276dcbe3 ON public.analytics_company_event_feed_notification_logs USING btree (analytics_company_id);


--
-- Name: idx_on_brokerage_profile_id_type_80e1d23d0e; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_brokerage_profile_id_type_80e1d23d0e ON public.brokerage_profile_contacts USING btree (brokerage_profile_id, type);


--
-- Name: idx_on_brokerage_profile_id_user_id_98e9af502d; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_brokerage_profile_id_user_id_98e9af502d ON public.brokerage_profile_users USING btree (brokerage_profile_id, user_id);


--
-- Name: idx_on_carrier_availability_id_b5289feb8d; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_carrier_availability_id_b5289feb8d ON public.carrier_availability_destinations USING btree (carrier_availability_id);


--
-- Name: idx_on_carrier_network_builder_id_0e7dcf5116; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_carrier_network_builder_id_0e7dcf5116 ON public.carrier_network_builder_messages USING btree (carrier_network_builder_id);


--
-- Name: idx_on_city_id_specialized_service_id_daaf8747a8; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_city_id_specialized_service_id_daaf8747a8 ON public.city_specialized_services USING btree (city_id, specialized_service_id);


--
-- Name: idx_on_company_id_entity_type_588858bae1; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_company_id_entity_type_588858bae1 ON public.analytics_company_event_feeds USING btree (company_id, entity_type);


--
-- Name: idx_on_company_id_entity_type_provider_4a26d06715; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_company_id_entity_type_provider_4a26d06715 ON public.analytics_integrations USING btree (company_id, entity_type, provider);


--
-- Name: idx_on_company_id_specialized_service_id_68e50d972d; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_company_id_specialized_service_id_68e50d972d ON public.brokerages_specialized_services USING btree (company_id, specialized_service_id);


--
-- Name: idx_on_feed_id_notification_type_d9fe4a5539; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_feed_id_notification_type_d9fe4a5539 ON public.analytics_company_event_feed_notifications USING btree (feed_id, notification_type);


--
-- Name: idx_on_notification_id_00c18b2bce; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_notification_id_00c18b2bce ON public.analytics_company_event_feed_notification_logs USING btree (notification_id);


--
-- Name: idx_on_review_id_pickup_city_id_dropoff_city_id_e2077f3a02; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_review_id_pickup_city_id_dropoff_city_id_e2077f3a02 ON public.brokerage_reviews_review_lanes USING btree (review_id, pickup_city_id, dropoff_city_id);


--
-- Name: idx_on_review_id_sentiment_id_947e6894d5; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_review_id_sentiment_id_947e6894d5 ON public.brokerage_review_sentiments USING btree (review_id, sentiment_id);


--
-- Name: idx_on_sentiment_id_reviewer_reviewee_a658f5059d; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_sentiment_id_reviewer_reviewee_a658f5059d ON public.sentiment_personas USING btree (sentiment_id, reviewer, reviewee);


--
-- Name: idx_on_widget_id_brokerage_review_id_ea5db69857; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_on_widget_id_brokerage_review_id_ea5db69857 ON public.widget_brokerage_reviews USING btree (widget_id, brokerage_review_id);


--
-- Name: index_access_package_allotments_on_access_package_name_period; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_access_package_allotments_on_access_package_name_period ON public.access_package_allotments USING btree (access_package_id, name, period);


--
-- Name: index_access_packages_on_active_resource; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_access_packages_on_active_resource ON public.access_packages USING btree (resource_type, resource_id, active) WHERE (active = true);


--
-- Name: index_access_packages_on_subscription_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_access_packages_on_subscription_id ON public.access_packages USING btree (subscription_id);


--
-- Name: index_action_text_rich_texts_uniqueness; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_action_text_rich_texts_uniqueness ON public.action_text_rich_texts USING btree (record_type, record_id, name);


--
-- Name: index_active_admin_comments_on_author; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_active_admin_comments_on_author ON public.active_admin_comments USING btree (author_type, author_id);


--
-- Name: index_active_admin_comments_on_namespace; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_active_admin_comments_on_namespace ON public.active_admin_comments USING btree (namespace);


--
-- Name: index_active_admin_comments_on_resource; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_active_admin_comments_on_resource ON public.active_admin_comments USING btree (resource_type, resource_id);


--
-- Name: index_active_campaign_records_on_record; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_active_campaign_records_on_record ON public.active_campaign_records USING btree (record_type, record_id);


--
-- Name: index_active_storage_attachments_on_blob_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_active_storage_attachments_on_blob_id ON public.active_storage_attachments USING btree (blob_id);


--
-- Name: index_active_storage_attachments_uniqueness; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_active_storage_attachments_uniqueness ON public.active_storage_attachments USING btree (record_type, record_id, name, blob_id);


--
-- Name: index_active_storage_blobs_on_key; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_active_storage_blobs_on_key ON public.active_storage_blobs USING btree (key);


--
-- Name: index_active_storage_variant_records_uniqueness; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_active_storage_variant_records_uniqueness ON public.active_storage_variant_records USING btree (blob_id, variation_digest);


--
-- Name: index_analytics_companies_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_companies_on_city_id ON public.analytics_companies USING btree (city_id);


--
-- Name: index_analytics_companies_on_domain; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_companies_on_domain ON public.analytics_companies USING btree (domain);


--
-- Name: index_analytics_companies_on_industry_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_companies_on_industry_id ON public.analytics_companies USING btree (industry_id);


--
-- Name: index_analytics_companies_on_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_companies_on_name ON public.analytics_companies USING gin (name public.gin_trgm_ops);


--
-- Name: index_analytics_companies_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_companies_on_uuid ON public.analytics_companies USING btree (uuid);


--
-- Name: index_analytics_company_event_feed_exports_on_feed_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_event_feed_exports_on_feed_id ON public.analytics_company_event_feed_exports USING btree (feed_id);


--
-- Name: index_analytics_company_event_feed_exports_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_event_feed_exports_on_user_id ON public.analytics_company_event_feed_exports USING btree (user_id);


--
-- Name: index_analytics_company_event_feed_notification_logs_on_synced; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_event_feed_notification_logs_on_synced ON public.analytics_company_event_feed_notification_logs USING btree (synced);


--
-- Name: index_analytics_company_event_feed_notifications_on_feed_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_event_feed_notifications_on_feed_id ON public.analytics_company_event_feed_notifications USING btree (feed_id);


--
-- Name: index_analytics_company_event_feed_notifications_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_company_event_feed_notifications_on_uuid ON public.analytics_company_event_feed_notifications USING btree (uuid);


--
-- Name: index_analytics_company_event_feeds_on_company_entity_editable; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_company_event_feeds_on_company_entity_editable ON public.analytics_company_event_feeds USING btree (company_id, entity_type, editable) WHERE (editable = false);


--
-- Name: index_analytics_company_event_feeds_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_company_event_feeds_on_uuid ON public.analytics_company_event_feeds USING btree (uuid);


--
-- Name: index_analytics_company_providers_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_providers_on_city_id ON public.analytics_company_providers USING btree (city_id);


--
-- Name: index_analytics_company_providers_on_domain_and_provider; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_company_providers_on_domain_and_provider ON public.analytics_company_providers USING btree (domain, provider);


--
-- Name: index_analytics_company_providers_on_industry_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_providers_on_industry_id ON public.analytics_company_providers USING btree (industry_id);


--
-- Name: index_analytics_company_providers_on_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_providers_on_name ON public.analytics_company_providers USING gin (name public.gin_trgm_ops);


--
-- Name: index_analytics_company_providers_on_provider; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_company_providers_on_provider ON public.analytics_company_providers USING btree (provider);


--
-- Name: index_analytics_industries_on_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_industries_on_name ON public.analytics_industries USING btree (name);


--
-- Name: index_analytics_integrations_on_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_analytics_integrations_on_status ON public.analytics_integrations USING btree (status);


--
-- Name: index_analytics_load_industries_on_industry_id_and_load_gid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_analytics_load_industries_on_industry_id_and_load_gid ON public.analytics_load_industries USING btree (industry_id, load_gid);


--
-- Name: index_api_tokens_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_api_tokens_on_user_id ON public.api_tokens USING btree (user_id);


--
-- Name: index_api_tokens_on_value; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_api_tokens_on_value ON public.api_tokens USING btree (value);


--
-- Name: index_audits_on_associated; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_audits_on_associated ON public.audits USING btree (associated_type, associated_id);


--
-- Name: index_audits_on_auditable_and_version; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_audits_on_auditable_and_version ON public.audits USING btree (auditable_type, auditable_id, version);


--
-- Name: index_audits_on_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_audits_on_created_at ON public.audits USING btree (created_at);


--
-- Name: index_audits_on_request_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_audits_on_request_uuid ON public.audits USING btree (request_uuid);


--
-- Name: index_audits_on_user; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_audits_on_user ON public.audits USING btree (user_id, user_type);


--
-- Name: index_authentications_on_provider_and_uid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_authentications_on_provider_and_uid ON public.authentications USING btree (provider, uid);


--
-- Name: index_authentications_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_authentications_on_user_id ON public.authentications USING btree (user_id);


--
-- Name: index_authhist_on_docket_number; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_authhist_on_docket_number ON public.authhist USING btree (docket_number);


--
-- Name: index_bookmarks_on_list_id_and_company_id_and_entity_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_bookmarks_on_list_id_and_company_id_and_entity_type ON public.bookmarks USING btree (list_id, company_id, entity_type);


--
-- Name: index_brokerage_cities_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_cities_on_city_id ON public.brokerage_cities USING btree (city_id);


--
-- Name: index_brokerage_domains_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_domains_on_company_id ON public.brokerage_domains USING btree (company_id);


--
-- Name: index_brokerage_onboardings_on_brokerage_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_onboardings_on_brokerage_profile_id ON public.brokerage_onboardings USING btree (brokerage_profile_id);


--
-- Name: index_brokerage_profile_assets_on_brokerage_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_profile_assets_on_brokerage_profile_id ON public.brokerage_profile_assets USING btree (brokerage_profile_id);


--
-- Name: index_brokerage_profile_preferred_lanes_on_profile_and_cities; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_profile_preferred_lanes_on_profile_and_cities ON public.brokerage_profile_preferred_lanes USING btree (brokerage_profile_id, pickup_city_id, dropoff_city_id);


--
-- Name: index_brokerage_profile_users_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_profile_users_on_user_id ON public.brokerage_profile_users USING btree (user_id);


--
-- Name: index_brokerage_profile_widgets_on_brokerage_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_profile_widgets_on_brokerage_profile_id ON public.brokerage_profile_widgets USING btree (brokerage_profile_id);


--
-- Name: index_brokerage_profile_widgets_on_widget_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_profile_widgets_on_widget_id ON public.brokerage_profile_widgets USING btree (widget_id);


--
-- Name: index_brokerage_profiles_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_profiles_on_company_id ON public.brokerage_profiles USING btree (company_id);


--
-- Name: index_brokerage_review_aggregates_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_review_aggregates_on_company_id ON public.brokerage_review_aggregates USING btree (company_id);


--
-- Name: index_brokerage_review_replies_on_review_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_review_replies_on_review_id ON public.brokerage_review_replies USING btree (review_id);


--
-- Name: index_brokerage_review_replies_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_review_replies_on_user_id ON public.brokerage_review_replies USING btree (user_id);


--
-- Name: index_brokerage_review_reports_on_review_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_review_reports_on_review_id ON public.brokerage_review_reports USING btree (review_id);


--
-- Name: index_brokerage_review_reports_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_review_reports_on_user_id ON public.brokerage_review_reports USING btree (user_id);


--
-- Name: index_brokerage_review_sentiments_on_sentiment_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_review_sentiments_on_sentiment_id ON public.brokerage_review_sentiments USING btree (sentiment_id);


--
-- Name: index_brokerage_reviews_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_on_company_id ON public.brokerage_reviews USING btree (company_id);


--
-- Name: index_brokerage_reviews_on_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_on_created_at ON public.brokerage_reviews USING btree (created_at);


--
-- Name: index_brokerage_reviews_on_featured; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_on_featured ON public.brokerage_reviews USING btree (featured);


--
-- Name: index_brokerage_reviews_on_industry_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_on_industry_id ON public.brokerage_reviews USING btree (industry_id);


--
-- Name: index_brokerage_reviews_on_persona_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_on_persona_id ON public.brokerage_reviews USING btree (persona_id);


--
-- Name: index_brokerage_reviews_on_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_on_status ON public.brokerage_reviews USING btree (status);


--
-- Name: index_brokerage_reviews_on_user_id_and_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerage_reviews_on_user_id_and_company_id ON public.brokerage_reviews USING btree (user_id, company_id);


--
-- Name: index_brokerage_reviews_on_utm_param_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_on_utm_param_id ON public.brokerage_reviews USING btree (utm_param_id);


--
-- Name: index_brokerage_reviews_review_lanes_on_dropoff_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_review_lanes_on_dropoff_city_id ON public.brokerage_reviews_review_lanes USING btree (dropoff_city_id);


--
-- Name: index_brokerage_reviews_review_lanes_on_pickup_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_brokerage_reviews_review_lanes_on_pickup_city_id ON public.brokerage_reviews_review_lanes USING btree (pickup_city_id);


--
-- Name: index_brokerages_review_lanes_on_company_pickup_and_dropoff; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerages_review_lanes_on_company_pickup_and_dropoff ON public.brokerages_review_lanes USING btree (company_id, pickup_city_id, dropoff_city_id);


--
-- Name: index_brokerages_shipment_types_on_company_and_shipment_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerages_shipment_types_on_company_and_shipment_type ON public.brokerages_shipment_types USING btree (company_id, shipment_type_id);


--
-- Name: index_brokerages_truck_types_on_company_and_truck_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_brokerages_truck_types_on_company_and_truck_type ON public.brokerages_truck_types USING btree (company_id, truck_type_id);


--
-- Name: index_carrier_availabilities_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_availabilities_on_company_id ON public.carrier_availabilities USING btree (company_id);


--
-- Name: index_carrier_availabilities_on_shipment_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_availabilities_on_shipment_type_id ON public.carrier_availabilities USING btree (shipment_type_id);


--
-- Name: index_carrier_availabilities_on_truck_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_availabilities_on_truck_type_id ON public.carrier_availabilities USING btree (truck_type_id);


--
-- Name: index_carrier_availability_destinations_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_availability_destinations_on_city_id ON public.carrier_availability_destinations USING btree (city_id);


--
-- Name: index_carrier_availability_origins_on_carrier_availability_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_availability_origins_on_carrier_availability_id ON public.carrier_availability_origins USING btree (carrier_availability_id);


--
-- Name: index_carrier_availability_origins_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_availability_origins_on_city_id ON public.carrier_availability_origins USING btree (city_id);


--
-- Name: index_carrier_availability_unsubscribes_on_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_availability_unsubscribes_on_email ON public.carrier_availability_unsubscribes USING btree (email);


--
-- Name: index_carrier_cities_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_cities_on_city_id ON public.carrier_cities USING btree (city_id);


--
-- Name: index_carrier_csv_exports_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_csv_exports_on_user_id ON public.carrier_csv_exports USING btree (user_id);


--
-- Name: index_carrier_network_builder_lane_entities_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_network_builder_lane_entities_on_company_id ON public.carrier_network_builder_lane_entities USING btree (company_id);


--
-- Name: index_carrier_network_builder_lane_entities_on_lane_and_entity; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_network_builder_lane_entities_on_lane_and_entity ON public.carrier_network_builder_lane_entities USING btree (carrier_network_builder_lane_id, company_id, entity_type);


--
-- Name: index_carrier_network_builder_lanes_on_origin_destination; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_network_builder_lanes_on_origin_destination ON public.carrier_network_builder_lanes USING btree (carrier_network_builder_id, origin_id, destination_id);


--
-- Name: index_carrier_network_builders_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_network_builders_on_user_id ON public.carrier_network_builders USING btree (user_id);


--
-- Name: index_carrier_network_builders_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_network_builders_on_uuid ON public.carrier_network_builders USING btree (uuid);


--
-- Name: index_carrier_operation_states_on_carrier_profile_and_state_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_operation_states_on_carrier_profile_and_state_id ON public.carrier_operation_states USING btree (carrier_profile_id, state_id);


--
-- Name: index_carrier_profile_assets_on_carrier_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_profile_assets_on_carrier_profile_id ON public.carrier_profile_assets USING btree (carrier_profile_id);


--
-- Name: index_carrier_profile_contacts_on_carrier_profile_id_and_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profile_contacts_on_carrier_profile_id_and_type ON public.carrier_profile_contacts USING btree (carrier_profile_id, type);


--
-- Name: index_carrier_profile_terminals_on_carrier_profile_id_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profile_terminals_on_carrier_profile_id_city_id ON public.carrier_profile_terminals USING btree (carrier_profile_id, city_id);


--
-- Name: index_carrier_profile_users_on_carrier_profile_id_and_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profile_users_on_carrier_profile_id_and_user_id ON public.carrier_profile_users USING btree (carrier_profile_id, user_id);


--
-- Name: index_carrier_profile_users_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_profile_users_on_user_id ON public.carrier_profile_users USING btree (user_id);


--
-- Name: index_carrier_profile_website_highlights_on_website_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_profile_website_highlights_on_website_id ON public.carrier_profile_website_highlights USING btree (carrier_profile_website_id);


--
-- Name: index_carrier_profile_website_reviews_on_review_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_profile_website_reviews_on_review_id ON public.carrier_profile_website_reviews USING btree (review_id);


--
-- Name: index_carrier_profile_website_reviews_on_website_id_review_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profile_website_reviews_on_website_id_review_id ON public.carrier_profile_website_reviews USING btree (carrier_profile_website_id, review_id);


--
-- Name: index_carrier_profile_website_services_carrier_profile_website; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_profile_website_services_carrier_profile_website ON public.carrier_profile_website_services USING btree (carrier_profile_website_id);


--
-- Name: index_carrier_profile_websites_on_carrier_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profile_websites_on_carrier_profile_id ON public.carrier_profile_websites USING btree (carrier_profile_id);


--
-- Name: index_carrier_profile_websites_on_domain; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profile_websites_on_domain ON public.carrier_profile_websites USING btree (domain);


--
-- Name: index_carrier_profile_widgets_on_carrier_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_carrier_profile_widgets_on_carrier_profile_id ON public.carrier_profile_widgets USING btree (carrier_profile_id);


--
-- Name: index_carrier_profile_widgets_on_widget_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profile_widgets_on_widget_id ON public.carrier_profile_widgets USING btree (widget_id);


--
-- Name: index_carrier_profiles_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carrier_profiles_on_company_id ON public.carrier_profiles USING btree (company_id);


--
-- Name: index_carriers_accidents_on_dot_number_and_bucket; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carriers_accidents_on_dot_number_and_bucket ON public.carriers_accidents USING btree (dot_number, bucket);


--
-- Name: index_carriers_review_lanes_on_company_pickup_city_dropoff_city; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carriers_review_lanes_on_company_pickup_city_dropoff_city ON public.carriers_review_lanes USING btree (company_id, pickup_city_id, dropoff_city_id);


--
-- Name: index_carriers_safety_violations_on_dot_number_and_basic_desc; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carriers_safety_violations_on_dot_number_and_basic_desc ON public.carriers_safety_violations USING btree (dot_number, basic_desc);


--
-- Name: index_carriers_shipment_types_on_company_and_shipment_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carriers_shipment_types_on_company_and_shipment_type ON public.carriers_shipment_types USING btree (company_id, shipment_type_id);


--
-- Name: index_carriers_specialized_services_on_company_and_service; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carriers_specialized_services_on_company_and_service ON public.carriers_specialized_services USING btree (company_id, specialized_service_id);


--
-- Name: index_carriers_truck_types_on_company_and_truck_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_carriers_truck_types_on_company_and_truck_type ON public.carriers_truck_types USING btree (company_id, truck_type_id);


--
-- Name: index_census_on_mcs_150_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_census_on_mcs_150_date ON public.census USING btree (mcs_150_date);


--
-- Name: index_censuses_on_dot_number; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_censuses_on_dot_number ON public.censuses USING btree (dot_number);


--
-- Name: index_censuses_on_mcs150_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_censuses_on_mcs150_date ON public.censuses USING btree (mcs150_date);


--
-- Name: index_cities_geography; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_cities_geography ON public.cities USING gist (((public.st_makepoint(longitude, latitude))::public.geography));


--
-- Name: index_cities_metadata_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_cities_metadata_on_city_id ON public.cities_metadata USING btree (city_id);


--
-- Name: index_cities_on_country_code_and_state_code_and_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_cities_on_country_code_and_state_code_and_name ON public.cities USING btree (country_code, state_code, name);


--
-- Name: index_cities_on_population; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_cities_on_population ON public.cities USING btree (population);


--
-- Name: index_cities_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_cities_on_slug ON public.cities USING btree (slug);


--
-- Name: index_cities_postal_codes_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_cities_postal_codes_on_city_id ON public.cities_postal_codes USING btree (city_id);


--
-- Name: index_cities_postal_codes_on_postal_code_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_cities_postal_codes_on_postal_code_id ON public.cities_postal_codes USING btree (postal_code_id);


--
-- Name: index_city_freights_on_city_id_and_freight_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_city_freights_on_city_id_and_freight_id ON public.city_freights USING btree (city_id, freight_id);


--
-- Name: index_city_freights_on_freight_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_city_freights_on_freight_id ON public.city_freights USING btree (freight_id);


--
-- Name: index_city_shipment_types_on_city_and_shipment_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_city_shipment_types_on_city_and_shipment_type ON public.city_shipment_types USING btree (city_id, shipment_type_id);


--
-- Name: index_city_shipment_types_on_shipment_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_city_shipment_types_on_shipment_type ON public.city_shipment_types USING btree (shipment_type_id);


--
-- Name: index_city_specialized_services_on_specialized_service_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_city_specialized_services_on_specialized_service_id ON public.city_specialized_services USING btree (specialized_service_id);


--
-- Name: index_city_truck_types_on_city_and_truck_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_city_truck_types_on_city_and_truck_type ON public.city_truck_types USING btree (city_id, truck_type_id);


--
-- Name: index_city_truck_types_on_truck_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_city_truck_types_on_truck_type ON public.city_truck_types USING btree (truck_type_id);


--
-- Name: index_claim_partners_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_claim_partners_on_slug ON public.claim_partners USING btree (slug);


--
-- Name: index_companies_freights_on_company_id_and_freight_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_companies_freights_on_company_id_and_freight_id ON public.companies_freights USING btree (company_id, freight_id);


--
-- Name: index_companies_on_active_in_census; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_active_in_census ON public.companies USING btree (active_in_census);


--
-- Name: index_companies_on_census_change_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_census_change_date ON public.companies USING btree (census_change_date);


--
-- Name: index_companies_on_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_city_id ON public.companies USING btree (city_id);


--
-- Name: index_companies_on_claim_token; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_companies_on_claim_token ON public.companies USING btree (claim_token);


--
-- Name: index_companies_on_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_created_at ON public.companies USING btree (created_at);


--
-- Name: index_companies_on_dot_number; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_companies_on_dot_number ON public.companies USING btree (dot_number);


--
-- Name: index_companies_on_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_email ON public.companies USING btree (email);


--
-- Name: index_companies_on_hidden; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_hidden ON public.companies USING btree (hidden);


--
-- Name: index_companies_on_hidden_and_passengers_only; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_hidden_and_passengers_only ON public.companies USING btree (hidden, passengers_only) WHERE ((hidden = false) AND (passengers_only = false));


--
-- Name: index_companies_on_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_name ON public.companies USING gin (name public.gin_trgm_ops);


--
-- Name: index_companies_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_companies_on_slug ON public.companies USING btree (slug);


--
-- Name: index_companies_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_companies_on_uuid ON public.companies USING btree (uuid);


--
-- Name: index_companies_on_zip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_companies_on_zip ON public.companies USING btree (zip);


--
-- Name: index_companies_related_companies_on_company_related_company; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_companies_related_companies_on_company_related_company ON public.companies_related_companies USING btree (company_id, related_company_id);


--
-- Name: index_company_entity_types_on_company_id_and_entity_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_company_entity_types_on_company_id_and_entity_type ON public.company_entity_types USING btree (company_id, entity_type);


--
-- Name: index_company_notes_on_user_id_and_company_id_and_entity_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_company_notes_on_user_id_and_company_id_and_entity_type ON public.company_notes USING btree (user_id, company_id, entity_type);


--
-- Name: index_crash_on_crash_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_crash_on_crash_id ON public.crash USING btree (crash_id);


--
-- Name: index_crash_on_dot_number; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_crash_on_dot_number ON public.crash USING btree (dot_number);


--
-- Name: index_dev_account_permissions_on_account_and_endpoint; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_dev_account_permissions_on_account_and_endpoint ON public.developer_account_permissions USING btree (developer_account_id, endpoint);


--
-- Name: index_developer_account_users_on_api_token; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_developer_account_users_on_api_token ON public.developer_account_users USING btree (api_token);


--
-- Name: index_developer_account_users_on_developer_account_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_developer_account_users_on_developer_account_id ON public.developer_account_users USING btree (developer_account_id);


--
-- Name: index_developer_account_users_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_developer_account_users_on_user_id ON public.developer_account_users USING btree (user_id);


--
-- Name: index_domain_certificates_on_domain; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_domain_certificates_on_domain ON public.domain_certificates USING btree (domain);


--
-- Name: index_driver_carrier_reviews_aggregates_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_driver_carrier_reviews_aggregates_on_company_id ON public.driver_carrier_reviews_aggregates USING btree (company_id);


--
-- Name: index_driver_carrier_reviews_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_driver_carrier_reviews_on_company_id ON public.driver_carrier_reviews USING btree (company_id);


--
-- Name: index_driver_carrier_reviews_on_user_id_and_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_driver_carrier_reviews_on_user_id_and_company_id ON public.driver_carrier_reviews USING btree (user_id, company_id);


--
-- Name: index_driver_employment_experiences_on_driver_job_application; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_driver_employment_experiences_on_driver_job_application ON public.driver_employment_experiences USING btree (driver_job_application_id);


--
-- Name: index_driver_interview_availabilities_on_driver_job_application; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_driver_interview_availabilities_on_driver_job_application ON public.driver_interview_availabilities USING btree (driver_job_application_id);


--
-- Name: index_driver_job_applications_on_driver_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_driver_job_applications_on_driver_job_id ON public.driver_job_applications USING btree (driver_job_id);


--
-- Name: index_driver_jobs_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_driver_jobs_on_company_id ON public.driver_jobs USING btree (company_id);


--
-- Name: index_email_domains_on_domain; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_email_domains_on_domain ON public.email_domains USING btree (domain);


--
-- Name: index_email_feedbacks_on_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_email_feedbacks_on_email ON public.email_feedbacks USING btree (email);


--
-- Name: index_email_feedbacks_on_feedback_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_email_feedbacks_on_feedback_type ON public.email_feedbacks USING btree (feedback_type);


--
-- Name: index_freights_on_header; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_freights_on_header ON public.freights USING btree (header);


--
-- Name: index_freights_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_freights_on_slug ON public.freights USING btree (slug);


--
-- Name: index_freights_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_freights_on_uuid ON public.freights USING btree (uuid);


--
-- Name: index_friendly_id_slugs_on_slug_and_sluggable_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_friendly_id_slugs_on_slug_and_sluggable_type ON public.friendly_id_slugs USING btree (slug, sluggable_type) WHERE (scope IS NULL);


--
-- Name: index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope ON public.friendly_id_slugs USING btree (slug, sluggable_type, scope);


--
-- Name: index_friendly_id_slugs_on_sluggable_type_and_sluggable_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_friendly_id_slugs_on_sluggable_type_and_sluggable_id ON public.friendly_id_slugs USING btree (sluggable_type, sluggable_id);


--
-- Name: index_incoming_webhook_events_on_source_and_external_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_incoming_webhook_events_on_source_and_external_id ON public.incoming_webhook_events USING btree (source, external_id);


--
-- Name: index_inspections_on_dot_number; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_inspections_on_dot_number ON public.inspections USING btree (dot_number);


--
-- Name: index_insurance_on_docket_number_and_insurance_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_insurance_on_docket_number_and_insurance_type ON public.insurance USING btree (docket_number, insurance_type);


--
-- Name: index_insurance_on_insurance_company; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_insurance_on_insurance_company ON public.insurance USING btree (insurance_company);


--
-- Name: index_lists_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_lists_on_user_id ON public.lists USING btree (user_id);


--
-- Name: index_monthly_page_views_on_date_and_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_monthly_page_views_on_date_and_company_id ON public.monthly_page_views USING btree (date, company_id);


--
-- Name: index_one_off_tasks_on_version; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_one_off_tasks_on_version ON public.one_off_tasks USING btree (version);


--
-- Name: index_operating_authorities_on_common_authority; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_operating_authorities_on_common_authority ON public.operating_authorities USING btree (common_authority);


--
-- Name: index_operating_authorities_on_contract_authority; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_operating_authorities_on_contract_authority ON public.operating_authorities USING btree (contract_authority);


--
-- Name: index_operating_authorities_on_docket_number; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_operating_authorities_on_docket_number ON public.operating_authorities USING btree (docket_number);


--
-- Name: index_operating_authorities_on_dot_number; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_operating_authorities_on_dot_number ON public.operating_authorities USING btree (dot_number);


--
-- Name: index_operating_authorities_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_operating_authorities_on_uuid ON public.operating_authorities USING btree (uuid);


--
-- Name: index_page_views_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_page_views_on_company_id ON public.page_views USING btree (company_id);


--
-- Name: index_page_views_on_date_and_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_page_views_on_date_and_company_id ON public.page_views USING btree (date, company_id);


--
-- Name: index_persona_verifications_on_persona_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_persona_verifications_on_persona_id ON public.persona_verifications USING btree (persona_id);


--
-- Name: index_personas_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_personas_on_company_id ON public.personas USING btree (company_id);


--
-- Name: index_personas_on_user_id_and_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_personas_on_user_id_and_type ON public.personas USING btree (user_id, type);


--
-- Name: index_personas_on_verification_token; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_personas_on_verification_token ON public.personas USING btree (verification_token);


--
-- Name: index_postal_codes_on_code_and_country_code; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_postal_codes_on_code_and_country_code ON public.postal_codes USING btree (code, country_code);


--
-- Name: index_preferred_lanes_on_carrier_profile_and_cities; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_preferred_lanes_on_carrier_profile_and_cities ON public.preferred_lanes USING btree (carrier_profile_id, pickup_city_id, dropoff_city_id);


--
-- Name: index_recently_vieweds_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_recently_vieweds_on_company_id ON public.recently_vieweds USING btree (company_id);


--
-- Name: index_recently_vieweds_on_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_recently_vieweds_on_created_at ON public.recently_vieweds USING btree (created_at);


--
-- Name: index_recently_vieweds_on_user_id_and_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_recently_vieweds_on_user_id_and_company_id ON public.recently_vieweds USING btree (user_id, company_id);


--
-- Name: index_replies_on_review_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_replies_on_review_id ON public.replies USING btree (review_id);


--
-- Name: index_replies_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_replies_on_user_id ON public.replies USING btree (user_id);


--
-- Name: index_reports_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_reports_on_user_id ON public.reports USING btree (user_id);


--
-- Name: index_request_for_proposals_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_request_for_proposals_on_company_id ON public.request_for_proposals USING btree (company_id);


--
-- Name: index_request_for_proposals_on_dropoff_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_request_for_proposals_on_dropoff_city_id ON public.request_for_proposals USING btree (dropoff_city_id);


--
-- Name: index_request_for_proposals_on_entity_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_request_for_proposals_on_entity_type ON public.request_for_proposals USING btree (entity_type);


--
-- Name: index_request_for_proposals_on_freight_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_request_for_proposals_on_freight_id ON public.request_for_proposals USING btree (freight_id);


--
-- Name: index_request_for_proposals_on_pickup_city_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_request_for_proposals_on_pickup_city_id ON public.request_for_proposals USING btree (pickup_city_id);


--
-- Name: index_request_for_proposals_on_truck_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_request_for_proposals_on_truck_type_id ON public.request_for_proposals USING btree (truck_type_id);


--
-- Name: index_request_for_proposals_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_request_for_proposals_on_user_id ON public.request_for_proposals USING btree (user_id);


--
-- Name: index_review_lanes_on_review_and_cities; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_review_lanes_on_review_and_cities ON public.review_lanes USING btree (review_id, pickup_city_id, dropoff_city_id);


--
-- Name: index_review_sentiments_on_review_id_and_sentiment_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_review_sentiments_on_review_id_and_sentiment_id ON public.review_sentiments USING btree (review_id, sentiment_id);


--
-- Name: index_reviews_aggregates_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_reviews_aggregates_on_company_id ON public.reviews_aggregates USING btree (company_id);


--
-- Name: index_reviews_on_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_reviews_on_company_id ON public.reviews USING btree (company_id);


--
-- Name: index_reviews_on_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_reviews_on_created_at ON public.reviews USING btree (created_at);


--
-- Name: index_reviews_on_featured; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_reviews_on_featured ON public.reviews USING btree (featured);


--
-- Name: index_reviews_on_form_version; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_reviews_on_form_version ON public.reviews USING btree (form_version);


--
-- Name: index_reviews_on_persona_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_reviews_on_persona_id ON public.reviews USING btree (persona_id);


--
-- Name: index_reviews_on_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_reviews_on_status ON public.reviews USING btree (status);


--
-- Name: index_reviews_on_user_id_and_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_reviews_on_user_id_and_company_id ON public.reviews USING btree (user_id, company_id);


--
-- Name: index_reviews_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_reviews_on_uuid ON public.reviews USING btree (uuid);


--
-- Name: index_role_actions_on_role_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_role_actions_on_role_id ON public.role_actions USING btree (role_id);


--
-- Name: index_rollups_on_dimensions_carrier_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_rollups_on_dimensions_carrier_id ON public.rollups USING hash (((dimensions ->> 'carrier_id'::text)));


--
-- Name: index_rollups_on_name_and_interval_and_time_and_dimensions; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_rollups_on_name_and_interval_and_time_and_dimensions ON public.rollups USING btree (name, "interval", "time", dimensions);


--
-- Name: index_sentiments_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_sentiments_on_slug ON public.sentiments USING btree (slug);


--
-- Name: index_shipment_types_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_shipment_types_on_slug ON public.shipment_types USING btree (slug);


--
-- Name: index_shipment_types_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_shipment_types_on_uuid ON public.shipment_types USING btree (uuid);


--
-- Name: index_specialized_services_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_specialized_services_on_slug ON public.specialized_services USING btree (slug);


--
-- Name: index_subscriptions_on_external_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_subscriptions_on_external_id ON public.subscriptions USING btree (external_id);


--
-- Name: index_subscriptions_on_resource_type_and_resource_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_subscriptions_on_resource_type_and_resource_id ON public.subscriptions USING btree (resource_type, resource_id);


--
-- Name: index_truck_types_on_key; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_truck_types_on_key ON public.truck_types USING btree (key);


--
-- Name: index_truck_types_on_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_truck_types_on_slug ON public.truck_types USING btree (slug);


--
-- Name: index_truck_types_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_truck_types_on_uuid ON public.truck_types USING btree (uuid);


--
-- Name: index_user_emails_on_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_user_emails_on_email ON public.user_emails USING btree (email) WHERE ((status)::text = 'verified'::text);


--
-- Name: index_user_emails_on_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_user_emails_on_user_id ON public.user_emails USING btree (user_id);


--
-- Name: index_user_roles_on_user_id_and_role_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_user_roles_on_user_id_and_role_id ON public.user_roles USING btree (user_id, role_id);


--
-- Name: index_users_on_analytics_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_users_on_analytics_company_id ON public.users USING btree (analytics_company_id);


--
-- Name: index_users_on_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_email ON public.users USING btree (email);


--
-- Name: index_users_on_remember_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_users_on_remember_token ON public.users USING btree (remember_token);


--
-- Name: index_users_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_uuid ON public.users USING btree (uuid);


--
-- Name: index_versions_on_item_type_and_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_versions_on_item_type_and_item_id ON public.versions USING btree (item_type, item_id);


--
-- Name: index_violation_on_dot_number_basic_desc_oos_indicator; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_violation_on_dot_number_basic_desc_oos_indicator ON public.violation USING btree (dot_number, basic_desc, oos_indicator);


--
-- Name: index_violation_on_dot_number_basic_desc_severity_weight; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_violation_on_dot_number_basic_desc_severity_weight ON public.violation USING btree (dot_number, basic_desc, severity_weight);


--
-- Name: index_webhooks_events_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_webhooks_events_on_uuid ON public.webhooks_events USING btree (uuid);


--
-- Name: index_webhooks_subscriptions_on_developer_account_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_webhooks_subscriptions_on_developer_account_id ON public.webhooks_subscriptions USING btree (developer_account_id);


--
-- Name: index_webhooks_subscriptions_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_webhooks_subscriptions_on_uuid ON public.webhooks_subscriptions USING btree (uuid);


--
-- Name: index_weekly_page_views_on_date_and_company_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_weekly_page_views_on_date_and_company_id ON public.weekly_page_views USING btree (date, company_id);


--
-- Name: index_widget_reviews_on_widget_id_and_review_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_widget_reviews_on_widget_id_and_review_id ON public.widget_reviews USING btree (widget_id, review_id);


--
-- Name: index_widgets_on_company_id_and_entity_type_and_widget_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_widgets_on_company_id_and_entity_type_and_widget_type ON public.widgets USING btree (company_id, entity_type, widget_type);


--
-- Name: index_widgets_on_enabled; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_widgets_on_enabled ON public.widgets USING btree (enabled);


--
-- Name: index_widgets_on_uuid; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_widgets_on_uuid ON public.widgets USING btree (uuid);


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx1; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx1;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx2; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx2;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx3; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx3;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx4; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx4;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx5; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx5;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx6; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx6;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx7; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx7;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_ty_idx8; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_ty_idx8;


--
-- Name: analytics_aggregate_shipper_e_company_id_company_entity_typ_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_2e8c16e15e ATTACH PARTITION public.analytics_aggregate_shipper_e_company_id_company_entity_typ_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_02_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_02_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_02_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_02_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_03_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_03_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_03_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_03_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_04_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_04_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_04_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_04_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_05_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_05_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_05_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_05_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_06_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_06_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_06_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_06_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_07_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_07_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_07_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_07_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_08_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_08_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_08_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_08_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_09_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_09_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_09_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_09_type_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_10_id_interval_date_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_id_interval_date_01e1f80846 ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_10_id_interval_date_idx;


--
-- Name: analytics_aggregate_shipper_events_2025_10_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_aggregate_shipper_events_on_type ATTACH PARTITION public.analytics_aggregate_shipper_events_2025_10_type_idx;


--
-- Name: analytics_aggregate_shipper_events_202_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_202_analytics_company_id_idx;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx1; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx1;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx2; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx2;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx3; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx3;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx4; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx4;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx5; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx5;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx6; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx6;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx7; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx7;


--
-- Name: analytics_aggregate_shipper_events_20_analytics_company_id_idx8; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_analytics_company_id_3d7118b2d8 ATTACH PARTITION public.analytics_aggregate_shipper_events_20_analytics_company_id_idx8;


--
-- Name: analytics_events_2024_09_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2024_09_id_time_idx;


--
-- Name: analytics_events_2024_09_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2024_09_name_time_idx;


--
-- Name: analytics_events_2024_09_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2024_09_pkey;


--
-- Name: analytics_events_2024_09_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2024_09_properties_idx;


--
-- Name: analytics_events_2024_09_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2024_09_user_id_idx;


--
-- Name: analytics_events_2024_09_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2024_09_visit_id_idx;


--
-- Name: analytics_events_2024_10_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2024_10_id_time_idx;


--
-- Name: analytics_events_2024_10_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2024_10_name_time_idx;


--
-- Name: analytics_events_2024_10_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2024_10_pkey;


--
-- Name: analytics_events_2024_10_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2024_10_properties_idx;


--
-- Name: analytics_events_2024_10_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2024_10_user_id_idx;


--
-- Name: analytics_events_2024_10_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2024_10_visit_id_idx;


--
-- Name: analytics_events_2024_11_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2024_11_id_time_idx;


--
-- Name: analytics_events_2024_11_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2024_11_name_time_idx;


--
-- Name: analytics_events_2024_11_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2024_11_pkey;


--
-- Name: analytics_events_2024_11_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2024_11_properties_idx;


--
-- Name: analytics_events_2024_11_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2024_11_user_id_idx;


--
-- Name: analytics_events_2024_11_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2024_11_visit_id_idx;


--
-- Name: analytics_events_2024_12_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2024_12_id_time_idx;


--
-- Name: analytics_events_2024_12_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2024_12_name_time_idx;


--
-- Name: analytics_events_2024_12_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2024_12_pkey;


--
-- Name: analytics_events_2024_12_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2024_12_properties_idx;


--
-- Name: analytics_events_2024_12_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2024_12_user_id_idx;


--
-- Name: analytics_events_2024_12_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2024_12_visit_id_idx;


--
-- Name: analytics_events_2025_01_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_01_id_time_idx;


--
-- Name: analytics_events_2025_01_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_01_name_time_idx;


--
-- Name: analytics_events_2025_01_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_01_pkey;


--
-- Name: analytics_events_2025_01_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_01_properties_idx;


--
-- Name: analytics_events_2025_01_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_01_user_id_idx;


--
-- Name: analytics_events_2025_01_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_01_visit_id_idx;


--
-- Name: analytics_events_2025_02_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_02_id_time_idx;


--
-- Name: analytics_events_2025_02_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_02_name_time_idx;


--
-- Name: analytics_events_2025_02_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_02_pkey;


--
-- Name: analytics_events_2025_02_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_02_properties_idx;


--
-- Name: analytics_events_2025_02_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_02_user_id_idx;


--
-- Name: analytics_events_2025_02_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_02_visit_id_idx;


--
-- Name: analytics_events_2025_03_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_03_id_time_idx;


--
-- Name: analytics_events_2025_03_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_03_name_time_idx;


--
-- Name: analytics_events_2025_03_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_03_pkey;


--
-- Name: analytics_events_2025_03_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_03_properties_idx;


--
-- Name: analytics_events_2025_03_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_03_user_id_idx;


--
-- Name: analytics_events_2025_03_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_03_visit_id_idx;


--
-- Name: analytics_events_2025_04_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_04_id_time_idx;


--
-- Name: analytics_events_2025_04_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_04_name_time_idx;


--
-- Name: analytics_events_2025_04_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_04_pkey;


--
-- Name: analytics_events_2025_04_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_04_properties_idx;


--
-- Name: analytics_events_2025_04_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_04_user_id_idx;


--
-- Name: analytics_events_2025_04_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_04_visit_id_idx;


--
-- Name: analytics_events_2025_05_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_05_id_time_idx;


--
-- Name: analytics_events_2025_05_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_05_name_time_idx;


--
-- Name: analytics_events_2025_05_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_05_pkey;


--
-- Name: analytics_events_2025_05_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_05_properties_idx;


--
-- Name: analytics_events_2025_05_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_05_user_id_idx;


--
-- Name: analytics_events_2025_05_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_05_visit_id_idx;


--
-- Name: analytics_events_2025_06_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_06_id_time_idx;


--
-- Name: analytics_events_2025_06_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_06_name_time_idx;


--
-- Name: analytics_events_2025_06_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_06_pkey;


--
-- Name: analytics_events_2025_06_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_06_properties_idx;


--
-- Name: analytics_events_2025_06_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_06_user_id_idx;


--
-- Name: analytics_events_2025_06_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_06_visit_id_idx;


--
-- Name: analytics_events_2025_07_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_07_id_time_idx;


--
-- Name: analytics_events_2025_07_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_07_name_time_idx;


--
-- Name: analytics_events_2025_07_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_07_pkey;


--
-- Name: analytics_events_2025_07_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_07_properties_idx;


--
-- Name: analytics_events_2025_07_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_07_user_id_idx;


--
-- Name: analytics_events_2025_07_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_07_visit_id_idx;


--
-- Name: analytics_events_2025_08_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_08_id_time_idx;


--
-- Name: analytics_events_2025_08_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_08_name_time_idx;


--
-- Name: analytics_events_2025_08_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_08_pkey;


--
-- Name: analytics_events_2025_08_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_08_properties_idx;


--
-- Name: analytics_events_2025_08_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_08_user_id_idx;


--
-- Name: analytics_events_2025_08_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_08_visit_id_idx;


--
-- Name: analytics_events_2025_09_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_09_id_time_idx;


--
-- Name: analytics_events_2025_09_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_09_name_time_idx;


--
-- Name: analytics_events_2025_09_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_09_pkey;


--
-- Name: analytics_events_2025_09_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_09_properties_idx;


--
-- Name: analytics_events_2025_09_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_09_user_id_idx;


--
-- Name: analytics_events_2025_09_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_09_visit_id_idx;


--
-- Name: analytics_events_2025_10_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_id_and_time ATTACH PARTITION public.analytics_events_2025_10_id_time_idx;


--
-- Name: analytics_events_2025_10_name_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_name_and_time ATTACH PARTITION public.analytics_events_2025_10_name_time_idx;


--
-- Name: analytics_events_2025_10_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_events_pkey ATTACH PARTITION public.analytics_events_2025_10_pkey;


--
-- Name: analytics_events_2025_10_properties_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_properties ATTACH PARTITION public.analytics_events_2025_10_properties_idx;


--
-- Name: analytics_events_2025_10_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_user_id ATTACH PARTITION public.analytics_events_2025_10_user_id_idx;


--
-- Name: analytics_events_2025_10_visit_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_events_on_visit_id ATTACH PARTITION public.analytics_events_2025_10_visit_id_idx;


--
-- Name: analytics_shipper_events_2024_09_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2024_09_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2024_09_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2024_09_event_id_idx;


--
-- Name: analytics_shipper_events_2024_09_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2024_09_id_time_idx;


--
-- Name: analytics_shipper_events_2024_09_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2024_09_type_idx;


--
-- Name: analytics_shipper_events_2024_10_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2024_10_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2024_10_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2024_10_event_id_idx;


--
-- Name: analytics_shipper_events_2024_10_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2024_10_id_time_idx;


--
-- Name: analytics_shipper_events_2024_10_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2024_10_type_idx;


--
-- Name: analytics_shipper_events_2024_11_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2024_11_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2024_11_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2024_11_event_id_idx;


--
-- Name: analytics_shipper_events_2024_11_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2024_11_id_time_idx;


--
-- Name: analytics_shipper_events_2024_11_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2024_11_type_idx;


--
-- Name: analytics_shipper_events_2024_12_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2024_12_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2024_12_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2024_12_event_id_idx;


--
-- Name: analytics_shipper_events_2024_12_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2024_12_id_time_idx;


--
-- Name: analytics_shipper_events_2024_12_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2024_12_type_idx;


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx2; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2024_company_id_company_entity_ty_idx2;


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx3; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2024_company_id_company_entity_ty_idx3;


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx4; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2024_company_id_company_entity_ty_idx4;


--
-- Name: analytics_shipper_events_2024_company_id_company_entity_ty_idx5; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2024_company_id_company_entity_ty_idx5;


--
-- Name: analytics_shipper_events_2025_01_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_01_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_01_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_01_event_id_idx;


--
-- Name: analytics_shipper_events_2025_01_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_01_id_time_idx;


--
-- Name: analytics_shipper_events_2025_01_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_01_type_idx;


--
-- Name: analytics_shipper_events_2025_02_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_02_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_02_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_02_event_id_idx;


--
-- Name: analytics_shipper_events_2025_02_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_02_id_time_idx;


--
-- Name: analytics_shipper_events_2025_02_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_02_type_idx;


--
-- Name: analytics_shipper_events_2025_03_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_03_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_03_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_03_event_id_idx;


--
-- Name: analytics_shipper_events_2025_03_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_03_id_time_idx;


--
-- Name: analytics_shipper_events_2025_03_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_03_type_idx;


--
-- Name: analytics_shipper_events_2025_04_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_04_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_04_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_04_event_id_idx;


--
-- Name: analytics_shipper_events_2025_04_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_04_id_time_idx;


--
-- Name: analytics_shipper_events_2025_04_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_04_type_idx;


--
-- Name: analytics_shipper_events_2025_05_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_05_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_05_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_05_event_id_idx;


--
-- Name: analytics_shipper_events_2025_05_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_05_id_time_idx;


--
-- Name: analytics_shipper_events_2025_05_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_05_type_idx;


--
-- Name: analytics_shipper_events_2025_06_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_06_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_06_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_06_event_id_idx;


--
-- Name: analytics_shipper_events_2025_06_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_06_id_time_idx;


--
-- Name: analytics_shipper_events_2025_06_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_06_type_idx;


--
-- Name: analytics_shipper_events_2025_07_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_07_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_07_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_07_event_id_idx;


--
-- Name: analytics_shipper_events_2025_07_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_07_id_time_idx;


--
-- Name: analytics_shipper_events_2025_07_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_07_type_idx;


--
-- Name: analytics_shipper_events_2025_08_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_08_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_08_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_08_event_id_idx;


--
-- Name: analytics_shipper_events_2025_08_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_08_id_time_idx;


--
-- Name: analytics_shipper_events_2025_08_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_08_type_idx;


--
-- Name: analytics_shipper_events_2025_09_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_09_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_09_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_09_event_id_idx;


--
-- Name: analytics_shipper_events_2025_09_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_09_id_time_idx;


--
-- Name: analytics_shipper_events_2025_09_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_09_type_idx;


--
-- Name: analytics_shipper_events_2025_10_analytics_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_analytics_company_id ATTACH PARTITION public.analytics_shipper_events_2025_10_analytics_company_id_idx;


--
-- Name: analytics_shipper_events_2025_10_event_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_event_id ATTACH PARTITION public.analytics_shipper_events_2025_10_event_id_idx;


--
-- Name: analytics_shipper_events_2025_10_id_time_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_id_and_time ATTACH PARTITION public.analytics_shipper_events_2025_10_id_time_idx;


--
-- Name: analytics_shipper_events_2025_10_type_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_shipper_events_on_type ATTACH PARTITION public.analytics_shipper_events_2025_10_type_idx;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx1; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx1;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx2; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx2;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx3; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx3;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx4; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx4;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx5; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx5;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx6; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx6;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx7; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx7;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx8; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx8;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_ty_idx9; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_ty_idx9;


--
-- Name: analytics_shipper_events_2025_company_id_company_entity_typ_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.idx_on_company_id_company_entity_type_44355d96dd ATTACH PARTITION public.analytics_shipper_events_2025_company_id_company_entity_typ_idx;


--
-- Name: analytics_visits_2024_09_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2024_09_company_id_idx;


--
-- Name: analytics_visits_2024_09_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2024_09_id_started_at_idx;


--
-- Name: analytics_visits_2024_09_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2024_09_pkey;


--
-- Name: analytics_visits_2024_09_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2024_09_user_id_idx;


--
-- Name: analytics_visits_2024_09_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_09_visit_token_started_at_idx;


--
-- Name: analytics_visits_2024_09_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_09_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2024_10_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2024_10_company_id_idx;


--
-- Name: analytics_visits_2024_10_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2024_10_id_started_at_idx;


--
-- Name: analytics_visits_2024_10_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2024_10_pkey;


--
-- Name: analytics_visits_2024_10_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2024_10_user_id_idx;


--
-- Name: analytics_visits_2024_10_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_10_visit_token_started_at_idx;


--
-- Name: analytics_visits_2024_10_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_10_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2024_11_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2024_11_company_id_idx;


--
-- Name: analytics_visits_2024_11_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2024_11_id_started_at_idx;


--
-- Name: analytics_visits_2024_11_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2024_11_pkey;


--
-- Name: analytics_visits_2024_11_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2024_11_user_id_idx;


--
-- Name: analytics_visits_2024_11_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_11_visit_token_started_at_idx;


--
-- Name: analytics_visits_2024_11_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_11_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2024_12_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2024_12_company_id_idx;


--
-- Name: analytics_visits_2024_12_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2024_12_id_started_at_idx;


--
-- Name: analytics_visits_2024_12_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2024_12_pkey;


--
-- Name: analytics_visits_2024_12_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2024_12_user_id_idx;


--
-- Name: analytics_visits_2024_12_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_12_visit_token_started_at_idx;


--
-- Name: analytics_visits_2024_12_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2024_12_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_01_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_01_company_id_idx;


--
-- Name: analytics_visits_2025_01_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_01_id_started_at_idx;


--
-- Name: analytics_visits_2025_01_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_01_pkey;


--
-- Name: analytics_visits_2025_01_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_01_user_id_idx;


--
-- Name: analytics_visits_2025_01_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_01_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_01_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_01_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_02_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_02_company_id_idx;


--
-- Name: analytics_visits_2025_02_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_02_id_started_at_idx;


--
-- Name: analytics_visits_2025_02_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_02_pkey;


--
-- Name: analytics_visits_2025_02_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_02_user_id_idx;


--
-- Name: analytics_visits_2025_02_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_02_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_02_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_02_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_03_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_03_company_id_idx;


--
-- Name: analytics_visits_2025_03_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_03_id_started_at_idx;


--
-- Name: analytics_visits_2025_03_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_03_pkey;


--
-- Name: analytics_visits_2025_03_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_03_user_id_idx;


--
-- Name: analytics_visits_2025_03_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_03_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_03_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_03_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_04_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_04_company_id_idx;


--
-- Name: analytics_visits_2025_04_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_04_id_started_at_idx;


--
-- Name: analytics_visits_2025_04_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_04_pkey;


--
-- Name: analytics_visits_2025_04_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_04_user_id_idx;


--
-- Name: analytics_visits_2025_04_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_04_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_04_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_04_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_05_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_05_company_id_idx;


--
-- Name: analytics_visits_2025_05_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_05_id_started_at_idx;


--
-- Name: analytics_visits_2025_05_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_05_pkey;


--
-- Name: analytics_visits_2025_05_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_05_user_id_idx;


--
-- Name: analytics_visits_2025_05_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_05_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_05_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_05_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_06_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_06_company_id_idx;


--
-- Name: analytics_visits_2025_06_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_06_id_started_at_idx;


--
-- Name: analytics_visits_2025_06_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_06_pkey;


--
-- Name: analytics_visits_2025_06_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_06_user_id_idx;


--
-- Name: analytics_visits_2025_06_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_06_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_06_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_06_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_07_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_07_company_id_idx;


--
-- Name: analytics_visits_2025_07_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_07_id_started_at_idx;


--
-- Name: analytics_visits_2025_07_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_07_pkey;


--
-- Name: analytics_visits_2025_07_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_07_user_id_idx;


--
-- Name: analytics_visits_2025_07_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_07_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_07_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_07_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_08_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_08_company_id_idx;


--
-- Name: analytics_visits_2025_08_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_08_id_started_at_idx;


--
-- Name: analytics_visits_2025_08_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_08_pkey;


--
-- Name: analytics_visits_2025_08_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_08_user_id_idx;


--
-- Name: analytics_visits_2025_08_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_08_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_08_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_08_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_09_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_09_company_id_idx;


--
-- Name: analytics_visits_2025_09_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_09_id_started_at_idx;


--
-- Name: analytics_visits_2025_09_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_09_pkey;


--
-- Name: analytics_visits_2025_09_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_09_user_id_idx;


--
-- Name: analytics_visits_2025_09_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_09_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_09_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_09_visitor_token_started_at_idx;


--
-- Name: analytics_visits_2025_10_company_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_company_id ATTACH PARTITION public.analytics_visits_2025_10_company_id_idx;


--
-- Name: analytics_visits_2025_10_id_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_id_and_started_at ATTACH PARTITION public.analytics_visits_2025_10_id_started_at_idx;


--
-- Name: analytics_visits_2025_10_pkey; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.analytics_visits_pkey ATTACH PARTITION public.analytics_visits_2025_10_pkey;


--
-- Name: analytics_visits_2025_10_user_id_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_user_id ATTACH PARTITION public.analytics_visits_2025_10_user_id_idx;


--
-- Name: analytics_visits_2025_10_visit_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visit_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_10_visit_token_started_at_idx;


--
-- Name: analytics_visits_2025_10_visitor_token_started_at_idx; Type: INDEX ATTACH; Schema: public; Owner: -
--

ALTER INDEX public.index_analytics_visits_on_visitor_token_and_started_at ATTACH PARTITION public.analytics_visits_2025_10_visitor_token_started_at_idx;


--
-- Name: carrier_profile_websites fk_rails_00af1598de; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_websites
    ADD CONSTRAINT fk_rails_00af1598de FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id);


--
-- Name: reports fk_rails_022190ea4e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reports
    ADD CONSTRAINT fk_rails_022190ea4e FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: carrier_availabilities fk_rails_047f24e69e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availabilities
    ADD CONSTRAINT fk_rails_047f24e69e FOREIGN KEY (shipment_type_id) REFERENCES public.shipment_types(id);


--
-- Name: brokerage_reviews fk_rails_082ffeb549; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews
    ADD CONSTRAINT fk_rails_082ffeb549 FOREIGN KEY (persona_id) REFERENCES public.personas(id);


--
-- Name: authentications fk_rails_08833fecbe; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authentications
    ADD CONSTRAINT fk_rails_08833fecbe FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: brokerage_reviews fk_rails_0a25f14dae; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews
    ADD CONSTRAINT fk_rails_0a25f14dae FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: analytics_company_providers fk_rails_0a35ca8d1f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_providers
    ADD CONSTRAINT fk_rails_0a35ca8d1f FOREIGN KEY (industry_id) REFERENCES public.analytics_industries(id);


--
-- Name: driver_job_applications fk_rails_0d1dbec729; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_job_applications
    ADD CONSTRAINT fk_rails_0d1dbec729 FOREIGN KEY (utm_param_id) REFERENCES public.utm_params(id);


--
-- Name: carrier_profile_assets fk_rails_0d51a7c1fd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_assets
    ADD CONSTRAINT fk_rails_0d51a7c1fd FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id) ON DELETE CASCADE;


--
-- Name: analytics_company_event_feed_notification_logs fk_rails_0e0f61a48d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notification_logs
    ADD CONSTRAINT fk_rails_0e0f61a48d FOREIGN KEY (notification_id) REFERENCES public.analytics_company_event_feed_notifications(id) ON DELETE CASCADE;


--
-- Name: city_specialized_services fk_rails_1019330ae3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_specialized_services
    ADD CONSTRAINT fk_rails_1019330ae3 FOREIGN KEY (city_id) REFERENCES public.cities(id) ON DELETE CASCADE;


--
-- Name: carrier_cities fk_rails_1442fc3bf9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_cities
    ADD CONSTRAINT fk_rails_1442fc3bf9 FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: cities_postal_codes fk_rails_17d373ecbf; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_postal_codes
    ADD CONSTRAINT fk_rails_17d373ecbf FOREIGN KEY (postal_code_id) REFERENCES public.postal_codes(id) ON DELETE CASCADE;


--
-- Name: developer_account_users fk_rails_190c29dd6e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_account_users
    ADD CONSTRAINT fk_rails_190c29dd6e FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: carrier_profile_terminals fk_rails_1a97b9199b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_terminals
    ADD CONSTRAINT fk_rails_1a97b9199b FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: carrier_availabilities fk_rails_1e262d87be; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availabilities
    ADD CONSTRAINT fk_rails_1e262d87be FOREIGN KEY (truck_type_id) REFERENCES public.truck_types(id);


--
-- Name: carrier_csv_exports fk_rails_1fc2fa80e8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_csv_exports
    ADD CONSTRAINT fk_rails_1fc2fa80e8 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: carrier_availability_origins fk_rails_1fe4677fe7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_origins
    ADD CONSTRAINT fk_rails_1fe4677fe7 FOREIGN KEY (carrier_availability_id) REFERENCES public.carrier_availabilities(id) ON DELETE CASCADE;


--
-- Name: brokerage_review_aggregates fk_rails_218d960b3e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_aggregates
    ADD CONSTRAINT fk_rails_218d960b3e FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: brokerage_profile_widgets fk_rails_227e1a7429; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_widgets
    ADD CONSTRAINT fk_rails_227e1a7429 FOREIGN KEY (brokerage_profile_id) REFERENCES public.brokerage_profiles(id) ON DELETE CASCADE;


--
-- Name: analytics_company_event_feed_notifications fk_rails_23a123c535; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notifications
    ADD CONSTRAINT fk_rails_23a123c535 FOREIGN KEY (integration_id) REFERENCES public.analytics_integrations(id);


--
-- Name: companies fk_rails_268726594d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT fk_rails_268726594d FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: analytics_company_event_feed_notifications fk_rails_277cf63632; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notifications
    ADD CONSTRAINT fk_rails_277cf63632 FOREIGN KEY (feed_id) REFERENCES public.analytics_company_event_feeds(id) ON DELETE CASCADE;


--
-- Name: city_freights fk_rails_27a8e5fa49; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_freights
    ADD CONSTRAINT fk_rails_27a8e5fa49 FOREIGN KEY (freight_id) REFERENCES public.freights(id) ON DELETE CASCADE;


--
-- Name: lists fk_rails_28a57a2da2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.lists
    ADD CONSTRAINT fk_rails_28a57a2da2 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: bookmarks fk_rails_29c05dc16c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bookmarks
    ADD CONSTRAINT fk_rails_29c05dc16c FOREIGN KEY (list_id) REFERENCES public.lists(id) ON DELETE CASCADE;


--
-- Name: brokerages_truck_types fk_rails_2a218ac3d8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_truck_types
    ADD CONSTRAINT fk_rails_2a218ac3d8 FOREIGN KEY (truck_type_id) REFERENCES public.truck_types(id) ON DELETE CASCADE;


--
-- Name: request_for_proposals fk_rails_2a65a01b78; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_2a65a01b78 FOREIGN KEY (truck_type_id) REFERENCES public.truck_types(id);


--
-- Name: city_truck_types fk_rails_2b2e107c45; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_truck_types
    ADD CONSTRAINT fk_rails_2b2e107c45 FOREIGN KEY (city_id) REFERENCES public.cities(id) ON DELETE CASCADE;


--
-- Name: driver_carrier_reviews fk_rails_2b98cb2f34; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews
    ADD CONSTRAINT fk_rails_2b98cb2f34 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: brokerage_reviews fk_rails_2c3ecd7166; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews
    ADD CONSTRAINT fk_rails_2c3ecd7166 FOREIGN KEY (utm_param_id) REFERENCES public.utm_params(id);


--
-- Name: widget_brokerage_reviews fk_rails_2c594c3e9d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_brokerage_reviews
    ADD CONSTRAINT fk_rails_2c594c3e9d FOREIGN KEY (widget_id) REFERENCES public.widgets(id);


--
-- Name: city_specialized_services fk_rails_3119ea6401; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_specialized_services
    ADD CONSTRAINT fk_rails_3119ea6401 FOREIGN KEY (specialized_service_id) REFERENCES public.specialized_services(id) ON DELETE CASCADE;


--
-- Name: user_roles fk_rails_318345354e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT fk_rails_318345354e FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: carrier_profile_users fk_rails_325618645e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_users
    ADD CONSTRAINT fk_rails_325618645e FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id) ON DELETE CASCADE;


--
-- Name: user_roles fk_rails_3369e0d5fc; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT fk_rails_3369e0d5fc FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: carriers_review_lanes fk_rails_37edf5245a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_review_lanes
    ADD CONSTRAINT fk_rails_37edf5245a FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: carrier_availability_destinations fk_rails_38acfae4e6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_destinations
    ADD CONSTRAINT fk_rails_38acfae4e6 FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: request_for_proposals fk_rails_3a2f14aa26; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_3a2f14aa26 FOREIGN KEY (specialized_service_id) REFERENCES public.specialized_services(id);


--
-- Name: brokerage_profile_preferred_lanes fk_rails_3a35ebc27d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_preferred_lanes
    ADD CONSTRAINT fk_rails_3a35ebc27d FOREIGN KEY (brokerage_profile_id) REFERENCES public.brokerage_profiles(id) ON DELETE CASCADE;


--
-- Name: request_for_proposals fk_rails_3b5c01460c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_3b5c01460c FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: carrier_availabilities fk_rails_3be05abc1c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availabilities
    ADD CONSTRAINT fk_rails_3be05abc1c FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: webhooks_subscriptions fk_rails_3c0540127e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.webhooks_subscriptions
    ADD CONSTRAINT fk_rails_3c0540127e FOREIGN KEY (developer_account_id) REFERENCES public.developer_accounts(id) ON DELETE CASCADE;


--
-- Name: city_shipment_types fk_rails_3c38653bf4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_shipment_types
    ADD CONSTRAINT fk_rails_3c38653bf4 FOREIGN KEY (city_id) REFERENCES public.cities(id) ON DELETE CASCADE;


--
-- Name: carrier_profile_website_services fk_rails_3c67cc53b6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_services
    ADD CONSTRAINT fk_rails_3c67cc53b6 FOREIGN KEY (carrier_profile_website_id) REFERENCES public.carrier_profile_websites(id) ON DELETE CASCADE;


--
-- Name: review_lanes fk_rails_3fa1ff9f63; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_lanes
    ADD CONSTRAINT fk_rails_3fa1ff9f63 FOREIGN KEY (pickup_city_id) REFERENCES public.cities(id);


--
-- Name: user_emails fk_rails_410ac92848; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_emails
    ADD CONSTRAINT fk_rails_410ac92848 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: brokerage_profiles fk_rails_4171b2990a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profiles
    ADD CONSTRAINT fk_rails_4171b2990a FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: carrier_operation_states fk_rails_417aaab99d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_operation_states
    ADD CONSTRAINT fk_rails_417aaab99d FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id) ON DELETE CASCADE;


--
-- Name: personas fk_rails_424edcd6b0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personas
    ADD CONSTRAINT fk_rails_424edcd6b0 FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: carriers_truck_types fk_rails_4447a9c708; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_truck_types
    ADD CONSTRAINT fk_rails_4447a9c708 FOREIGN KEY (truck_type_id) REFERENCES public.truck_types(id) ON DELETE CASCADE;


--
-- Name: carrier_profile_website_highlights fk_rails_456224e106; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_highlights
    ADD CONSTRAINT fk_rails_456224e106 FOREIGN KEY (carrier_profile_website_id) REFERENCES public.carrier_profile_websites(id) ON DELETE CASCADE;


--
-- Name: brokerages_review_lanes fk_rails_47346dd969; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_review_lanes
    ADD CONSTRAINT fk_rails_47346dd969 FOREIGN KEY (pickup_city_id) REFERENCES public.cities(id);


--
-- Name: brokerage_review_reports fk_rails_490e6690b0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_reports
    ADD CONSTRAINT fk_rails_490e6690b0 FOREIGN KEY (review_id) REFERENCES public.brokerage_reviews(id) ON DELETE CASCADE;


--
-- Name: carriers_shipment_types fk_rails_4958841758; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_shipment_types
    ADD CONSTRAINT fk_rails_4958841758 FOREIGN KEY (shipment_type_id) REFERENCES public.shipment_types(id) ON DELETE CASCADE;


--
-- Name: analytics_integrations fk_rails_4b4440cbc9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_integrations
    ADD CONSTRAINT fk_rails_4b4440cbc9 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: driver_job_applications fk_rails_4b6cbb6424; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_job_applications
    ADD CONSTRAINT fk_rails_4b6cbb6424 FOREIGN KEY (driver_job_id) REFERENCES public.driver_jobs(id) ON DELETE CASCADE;


--
-- Name: reviews_aggregates fk_rails_4ba69e2a3c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews_aggregates
    ADD CONSTRAINT fk_rails_4ba69e2a3c FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: analytics_company_event_feed_exports fk_rails_4eeac4f3a6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_exports
    ADD CONSTRAINT fk_rails_4eeac4f3a6 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: carrier_profile_website_reviews fk_rails_4faf35d71b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_reviews
    ADD CONSTRAINT fk_rails_4faf35d71b FOREIGN KEY (review_id) REFERENCES public.reviews(id);


--
-- Name: review_lanes fk_rails_52af683a61; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_lanes
    ADD CONSTRAINT fk_rails_52af683a61 FOREIGN KEY (review_id) REFERENCES public.reviews(id) ON DELETE CASCADE;


--
-- Name: access_package_allotments fk_rails_55cd67a049; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.access_package_allotments
    ADD CONSTRAINT fk_rails_55cd67a049 FOREIGN KEY (access_package_id) REFERENCES public.access_packages(id) ON DELETE CASCADE;


--
-- Name: widget_reviews fk_rails_5f5b119156; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_reviews
    ADD CONSTRAINT fk_rails_5f5b119156 FOREIGN KEY (widget_id) REFERENCES public.widgets(id) ON DELETE CASCADE;


--
-- Name: carrier_network_builder_lanes fk_rails_60ac8dd0e2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_lanes
    ADD CONSTRAINT fk_rails_60ac8dd0e2 FOREIGN KEY (carrier_network_builder_id) REFERENCES public.carrier_network_builders(id) ON DELETE CASCADE;


--
-- Name: driver_job_applications fk_rails_61e6b35ac9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_job_applications
    ADD CONSTRAINT fk_rails_61e6b35ac9 FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: driver_carrier_reviews fk_rails_64ddfbd2ef; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews
    ADD CONSTRAINT fk_rails_64ddfbd2ef FOREIGN KEY (utm_param_id) REFERENCES public.utm_params(id);


--
-- Name: review_sentiments fk_rails_652ae9fab2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_sentiments
    ADD CONSTRAINT fk_rails_652ae9fab2 FOREIGN KEY (sentiment_id) REFERENCES public.sentiments(id);


--
-- Name: carrier_network_builder_lane_entities fk_rails_652d9c4044; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_lane_entities
    ADD CONSTRAINT fk_rails_652d9c4044 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: analytics_load_industries fk_rails_65aea5c7b3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_load_industries
    ADD CONSTRAINT fk_rails_65aea5c7b3 FOREIGN KEY (industry_id) REFERENCES public.analytics_industries(id) ON DELETE CASCADE;


--
-- Name: preferred_lanes fk_rails_678a15dfa7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.preferred_lanes
    ADD CONSTRAINT fk_rails_678a15dfa7 FOREIGN KEY (pickup_city_id) REFERENCES public.cities(id);


--
-- Name: brokerage_profile_preferred_lanes fk_rails_67993336cb; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_preferred_lanes
    ADD CONSTRAINT fk_rails_67993336cb FOREIGN KEY (pickup_city_id) REFERENCES public.cities(id);


--
-- Name: brokerage_reviews_review_lanes fk_rails_67f622c617; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews_review_lanes
    ADD CONSTRAINT fk_rails_67f622c617 FOREIGN KEY (review_id) REFERENCES public.brokerage_reviews(id) ON DELETE CASCADE;


--
-- Name: analytics_company_providers fk_rails_6855ec8e9b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_providers
    ADD CONSTRAINT fk_rails_6855ec8e9b FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: brokerages_shipment_types fk_rails_69d231475b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_shipment_types
    ADD CONSTRAINT fk_rails_69d231475b FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: carriers_specialized_services fk_rails_6c5baba69a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_specialized_services
    ADD CONSTRAINT fk_rails_6c5baba69a FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: request_for_proposals fk_rails_6d3f5958a2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_6d3f5958a2 FOREIGN KEY (freight_id) REFERENCES public.freights(id);


--
-- Name: carrier_profile_widgets fk_rails_6eac1fa464; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_widgets
    ADD CONSTRAINT fk_rails_6eac1fa464 FOREIGN KEY (widget_id) REFERENCES public.widgets(id) ON DELETE CASCADE;


--
-- Name: company_notes fk_rails_733d010328; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_notes
    ADD CONSTRAINT fk_rails_733d010328 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: brokerage_profile_users fk_rails_7580a1ed56; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_users
    ADD CONSTRAINT fk_rails_7580a1ed56 FOREIGN KEY (brokerage_profile_id) REFERENCES public.brokerage_profiles(id) ON DELETE CASCADE;


--
-- Name: brokerage_profile_users fk_rails_76bcf06bf4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_users
    ADD CONSTRAINT fk_rails_76bcf06bf4 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: widget_reviews fk_rails_76ce3b601d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_reviews
    ADD CONSTRAINT fk_rails_76ce3b601d FOREIGN KEY (review_id) REFERENCES public.reviews(id) ON DELETE CASCADE;


--
-- Name: brokerage_onboardings fk_rails_7a69e53772; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_onboardings
    ADD CONSTRAINT fk_rails_7a69e53772 FOREIGN KEY (brokerage_profile_id) REFERENCES public.brokerage_profiles(id);


--
-- Name: brokerage_reviews_review_lanes fk_rails_7ccd43f75d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews_review_lanes
    ADD CONSTRAINT fk_rails_7ccd43f75d FOREIGN KEY (pickup_city_id) REFERENCES public.cities(id);


--
-- Name: brokerages_specialized_services fk_rails_7d0855c994; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_specialized_services
    ADD CONSTRAINT fk_rails_7d0855c994 FOREIGN KEY (specialized_service_id) REFERENCES public.specialized_services(id) ON DELETE CASCADE;


--
-- Name: brokerage_cities fk_rails_8048188eb5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_cities
    ADD CONSTRAINT fk_rails_8048188eb5 FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: reviews fk_rails_80dc55d3ff; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT fk_rails_80dc55d3ff FOREIGN KEY (utm_param_id) REFERENCES public.utm_params(id);


--
-- Name: companies_freights fk_rails_81d57cbd2f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_freights
    ADD CONSTRAINT fk_rails_81d57cbd2f FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: analytics_company_event_feeds fk_rails_824f4f3338; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feeds
    ADD CONSTRAINT fk_rails_824f4f3338 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: analytics_company_event_feed_notification_logs fk_rails_8472c34d36; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_notification_logs
    ADD CONSTRAINT fk_rails_8472c34d36 FOREIGN KEY (analytics_company_id) REFERENCES public.analytics_companies(id) ON DELETE CASCADE;


--
-- Name: carrier_network_builder_messages fk_rails_852ac11204; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_messages
    ADD CONSTRAINT fk_rails_852ac11204 FOREIGN KEY (carrier_network_builder_id) REFERENCES public.carrier_network_builders(id) ON DELETE CASCADE;


--
-- Name: carrier_profile_widgets fk_rails_852e1d34c4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_widgets
    ADD CONSTRAINT fk_rails_852e1d34c4 FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id) ON DELETE CASCADE;


--
-- Name: carrier_profile_users fk_rails_8586895a48; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_users
    ADD CONSTRAINT fk_rails_8586895a48 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: brokerage_profile_preferred_lanes fk_rails_8a603bcc9d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_preferred_lanes
    ADD CONSTRAINT fk_rails_8a603bcc9d FOREIGN KEY (dropoff_city_id) REFERENCES public.cities(id);


--
-- Name: cities_postal_codes fk_rails_8c929e0223; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_postal_codes
    ADD CONSTRAINT fk_rails_8c929e0223 FOREIGN KEY (city_id) REFERENCES public.cities(id) ON DELETE CASCADE;


--
-- Name: developer_account_permissions fk_rails_8e9f6c5985; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_account_permissions
    ADD CONSTRAINT fk_rails_8e9f6c5985 FOREIGN KEY (developer_account_id) REFERENCES public.developer_accounts(id) ON DELETE CASCADE;


--
-- Name: reports fk_rails_8f5a68cdf0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reports
    ADD CONSTRAINT fk_rails_8f5a68cdf0 FOREIGN KEY (review_id) REFERENCES public.reviews(id) ON DELETE CASCADE;


--
-- Name: developer_account_users fk_rails_915203192e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.developer_account_users
    ADD CONSTRAINT fk_rails_915203192e FOREIGN KEY (developer_account_id) REFERENCES public.developer_accounts(id) ON DELETE CASCADE;


--
-- Name: brokerages_truck_types fk_rails_92bc7a433f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_truck_types
    ADD CONSTRAINT fk_rails_92bc7a433f FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: access_packages fk_rails_94926c2d34; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.access_packages
    ADD CONSTRAINT fk_rails_94926c2d34 FOREIGN KEY (subscription_id) REFERENCES public.subscriptions(id) ON DELETE CASCADE;


--
-- Name: persona_verifications fk_rails_95dd497475; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.persona_verifications
    ADD CONSTRAINT fk_rails_95dd497475 FOREIGN KEY (persona_id) REFERENCES public.personas(id) ON DELETE CASCADE;


--
-- Name: company_entity_types fk_rails_97f0e53189; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_entity_types
    ADD CONSTRAINT fk_rails_97f0e53189 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: companies_related_companies fk_rails_9804fd437d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_related_companies
    ADD CONSTRAINT fk_rails_9804fd437d FOREIGN KEY (related_company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: active_storage_variant_records fk_rails_993965df05; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_variant_records
    ADD CONSTRAINT fk_rails_993965df05 FOREIGN KEY (blob_id) REFERENCES public.active_storage_blobs(id);


--
-- Name: sentiment_personas fk_rails_9affa1055b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sentiment_personas
    ADD CONSTRAINT fk_rails_9affa1055b FOREIGN KEY (sentiment_id) REFERENCES public.sentiments(id) ON DELETE CASCADE;


--
-- Name: companies_related_companies fk_rails_9d89c649c4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_related_companies
    ADD CONSTRAINT fk_rails_9d89c649c4 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: reviews fk_rails_9ec8b0ddc0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT fk_rails_9ec8b0ddc0 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: brokerages_review_lanes fk_rails_9f547a73cf; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_review_lanes
    ADD CONSTRAINT fk_rails_9f547a73cf FOREIGN KEY (dropoff_city_id) REFERENCES public.cities(id);


--
-- Name: driver_jobs fk_rails_a630ea3ef1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_jobs
    ADD CONSTRAINT fk_rails_a630ea3ef1 FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: driver_carrier_reviews_aggregates fk_rails_a6d7cbfa4e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews_aggregates
    ADD CONSTRAINT fk_rails_a6d7cbfa4e FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: personas fk_rails_a7bee17002; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personas
    ADD CONSTRAINT fk_rails_a7bee17002 FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: request_for_proposals fk_rails_a8fb222350; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_a8fb222350 FOREIGN KEY (dropoff_city_id) REFERENCES public.cities(id);


--
-- Name: companies_freights fk_rails_a925df5df4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.companies_freights
    ADD CONSTRAINT fk_rails_a925df5df4 FOREIGN KEY (freight_id) REFERENCES public.freights(id) ON DELETE CASCADE;


--
-- Name: request_for_proposals fk_rails_a94ec0ee6d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_a94ec0ee6d FOREIGN KEY (shipment_type_id) REFERENCES public.shipment_types(id);


--
-- Name: carrier_profile_contacts fk_rails_adf4b41f07; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_contacts
    ADD CONSTRAINT fk_rails_adf4b41f07 FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id) ON DELETE CASCADE;


--
-- Name: brokerages_specialized_services fk_rails_ae41752cdd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_specialized_services
    ADD CONSTRAINT fk_rails_ae41752cdd FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: city_freights fk_rails_af78936d1d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_freights
    ADD CONSTRAINT fk_rails_af78936d1d FOREIGN KEY (city_id) REFERENCES public.cities(id) ON DELETE CASCADE;


--
-- Name: brokerage_review_sentiments fk_rails_b02c5db338; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_sentiments
    ADD CONSTRAINT fk_rails_b02c5db338 FOREIGN KEY (sentiment_id) REFERENCES public.sentiments(id);


--
-- Name: carrier_profile_website_reviews fk_rails_b053ac744d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_website_reviews
    ADD CONSTRAINT fk_rails_b053ac744d FOREIGN KEY (carrier_profile_website_id) REFERENCES public.carrier_profile_websites(id) ON DELETE CASCADE;


--
-- Name: review_lanes fk_rails_b05dba2c7d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_lanes
    ADD CONSTRAINT fk_rails_b05dba2c7d FOREIGN KEY (dropoff_city_id) REFERENCES public.cities(id);


--
-- Name: city_shipment_types fk_rails_b359a39908; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_shipment_types
    ADD CONSTRAINT fk_rails_b359a39908 FOREIGN KEY (shipment_type_id) REFERENCES public.shipment_types(id) ON DELETE CASCADE;


--
-- Name: driver_carrier_reviews fk_rails_b576c4d69d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_carrier_reviews
    ADD CONSTRAINT fk_rails_b576c4d69d FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: analytics_companies fk_rails_b62be61932; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_companies
    ADD CONSTRAINT fk_rails_b62be61932 FOREIGN KEY (industry_id) REFERENCES public.analytics_industries(id);


--
-- Name: carriers_specialized_services fk_rails_b69b175d49; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_specialized_services
    ADD CONSTRAINT fk_rails_b69b175d49 FOREIGN KEY (specialized_service_id) REFERENCES public.specialized_services(id);


--
-- Name: review_sentiments fk_rails_b69faca1fb; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.review_sentiments
    ADD CONSTRAINT fk_rails_b69faca1fb FOREIGN KEY (review_id) REFERENCES public.reviews(id);


--
-- Name: carriers_review_lanes fk_rails_b9428426ca; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_review_lanes
    ADD CONSTRAINT fk_rails_b9428426ca FOREIGN KEY (pickup_city_id) REFERENCES public.cities(id);


--
-- Name: carrier_availability_destinations fk_rails_b9a0bc07d1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_destinations
    ADD CONSTRAINT fk_rails_b9a0bc07d1 FOREIGN KEY (carrier_availability_id) REFERENCES public.carrier_availabilities(id) ON DELETE CASCADE;


--
-- Name: carrier_availability_origins fk_rails_bdd6f65c4f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_availability_origins
    ADD CONSTRAINT fk_rails_bdd6f65c4f FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: brokerage_review_replies fk_rails_be5c1d9899; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_replies
    ADD CONSTRAINT fk_rails_be5c1d9899 FOREIGN KEY (review_id) REFERENCES public.brokerage_reviews(id) ON DELETE CASCADE;


--
-- Name: widgets fk_rails_bf2836738f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widgets
    ADD CONSTRAINT fk_rails_bf2836738f FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: widget_brokerage_reviews fk_rails_c1e46bef2f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.widget_brokerage_reviews
    ADD CONSTRAINT fk_rails_c1e46bef2f FOREIGN KEY (brokerage_review_id) REFERENCES public.brokerage_reviews(id) ON DELETE CASCADE;


--
-- Name: brokerage_reviews fk_rails_c2fe205e2a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews
    ADD CONSTRAINT fk_rails_c2fe205e2a FOREIGN KEY (industry_id) REFERENCES public.analytics_industries(id);


--
-- Name: active_storage_attachments fk_rails_c3b3935057; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.active_storage_attachments
    ADD CONSTRAINT fk_rails_c3b3935057 FOREIGN KEY (blob_id) REFERENCES public.active_storage_blobs(id);


--
-- Name: carrier_profile_terminals fk_rails_c840de7d94; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profile_terminals
    ADD CONSTRAINT fk_rails_c840de7d94 FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id) ON DELETE CASCADE;


--
-- Name: brokerage_review_sentiments fk_rails_c8446a60cc; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_sentiments
    ADD CONSTRAINT fk_rails_c8446a60cc FOREIGN KEY (review_id) REFERENCES public.brokerage_reviews(id) ON DELETE CASCADE;


--
-- Name: analytics_company_event_feed_exports fk_rails_c88cbacf98; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_company_event_feed_exports
    ADD CONSTRAINT fk_rails_c88cbacf98 FOREIGN KEY (feed_id) REFERENCES public.analytics_company_event_feeds(id) ON DELETE CASCADE;


--
-- Name: city_truck_types fk_rails_c96755dee6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.city_truck_types
    ADD CONSTRAINT fk_rails_c96755dee6 FOREIGN KEY (truck_type_id) REFERENCES public.truck_types(id) ON DELETE CASCADE;


--
-- Name: cities_metadata fk_rails_c96b018b19; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_metadata
    ADD CONSTRAINT fk_rails_c96b018b19 FOREIGN KEY (city_id) REFERENCES public.cities(id) ON DELETE CASCADE;


--
-- Name: brokerage_reviews fk_rails_ccd53ae2f5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews
    ADD CONSTRAINT fk_rails_ccd53ae2f5 FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: brokerage_profile_assets fk_rails_cd222dc478; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_assets
    ADD CONSTRAINT fk_rails_cd222dc478 FOREIGN KEY (brokerage_profile_id) REFERENCES public.brokerage_profiles(id) ON DELETE CASCADE;


--
-- Name: carrier_network_builder_lane_entities fk_rails_d180ba59d4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_network_builder_lane_entities
    ADD CONSTRAINT fk_rails_d180ba59d4 FOREIGN KEY (carrier_network_builder_lane_id) REFERENCES public.carrier_network_builder_lanes(id) ON DELETE CASCADE;


--
-- Name: brokerage_profile_widgets fk_rails_d228864da8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_widgets
    ADD CONSTRAINT fk_rails_d228864da8 FOREIGN KEY (widget_id) REFERENCES public.widgets(id) ON DELETE CASCADE;


--
-- Name: reviews fk_rails_d253cfa138; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT fk_rails_d253cfa138 FOREIGN KEY (persona_id) REFERENCES public.personas(id);


--
-- Name: role_actions fk_rails_d472048504; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_actions
    ADD CONSTRAINT fk_rails_d472048504 FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: replies fk_rails_d4d1df8934; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.replies
    ADD CONSTRAINT fk_rails_d4d1df8934 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: carrier_profiles fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carrier_profiles
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: recently_vieweds fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.recently_vieweds
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: page_views fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.page_views
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: carriers_truck_types fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_truck_types
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: carriers_shipment_types fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_shipment_types
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: reviews fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: bookmarks fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.bookmarks
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_notes fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.company_notes
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: brokerage_domains fk_rails_d4e6c60946; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_domains
    ADD CONSTRAINT fk_rails_d4e6c60946 FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: request_for_proposals fk_rails_d5a9b4fd44; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_d5a9b4fd44 FOREIGN KEY (industry_id) REFERENCES public.analytics_industries(id);


--
-- Name: analytics_companies fk_rails_d8ea7a4304; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.analytics_companies
    ADD CONSTRAINT fk_rails_d8ea7a4304 FOREIGN KEY (city_id) REFERENCES public.cities(id);


--
-- Name: brokerage_review_reports fk_rails_dc0e0dfa98; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_reports
    ADD CONSTRAINT fk_rails_dc0e0dfa98 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: brokerages_shipment_types fk_rails_e34ca4b059; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_shipment_types
    ADD CONSTRAINT fk_rails_e34ca4b059 FOREIGN KEY (shipment_type_id) REFERENCES public.shipment_types(id) ON DELETE CASCADE;


--
-- Name: brokerage_reviews_review_lanes fk_rails_e43516f57c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_reviews_review_lanes
    ADD CONSTRAINT fk_rails_e43516f57c FOREIGN KEY (dropoff_city_id) REFERENCES public.cities(id);


--
-- Name: driver_employment_experiences fk_rails_e7ee7ba33a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_employment_experiences
    ADD CONSTRAINT fk_rails_e7ee7ba33a FOREIGN KEY (driver_job_application_id) REFERENCES public.driver_job_applications(id) ON DELETE CASCADE;


--
-- Name: driver_interview_availabilities fk_rails_e7ee7ba33a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_interview_availabilities
    ADD CONSTRAINT fk_rails_e7ee7ba33a FOREIGN KEY (driver_job_application_id) REFERENCES public.driver_job_applications(id) ON DELETE CASCADE;


--
-- Name: brokerage_review_replies fk_rails_e80af45e2c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_review_replies
    ADD CONSTRAINT fk_rails_e80af45e2c FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: request_for_proposals fk_rails_e8c98298a6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_e8c98298a6 FOREIGN KEY (pickup_city_id) REFERENCES public.cities(id);


--
-- Name: preferred_lanes fk_rails_eea3da039d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.preferred_lanes
    ADD CONSTRAINT fk_rails_eea3da039d FOREIGN KEY (carrier_profile_id) REFERENCES public.carrier_profiles(id) ON DELETE CASCADE;


--
-- Name: api_tokens fk_rails_f16b5e0447; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.api_tokens
    ADD CONSTRAINT fk_rails_f16b5e0447 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: carriers_review_lanes fk_rails_f3e3970195; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carriers_review_lanes
    ADD CONSTRAINT fk_rails_f3e3970195 FOREIGN KEY (dropoff_city_id) REFERENCES public.cities(id);


--
-- Name: preferred_lanes fk_rails_f40a603bcd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.preferred_lanes
    ADD CONSTRAINT fk_rails_f40a603bcd FOREIGN KEY (dropoff_city_id) REFERENCES public.cities(id);


--
-- Name: replies fk_rails_f4e0e099d2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.replies
    ADD CONSTRAINT fk_rails_f4e0e099d2 FOREIGN KEY (review_id) REFERENCES public.reviews(id) ON DELETE CASCADE;


--
-- Name: brokerages_review_lanes fk_rails_f8b7d8567e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerages_review_lanes
    ADD CONSTRAINT fk_rails_f8b7d8567e FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: brokerage_profile_contacts fk_rails_fc90d8e664; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.brokerage_profile_contacts
    ADD CONSTRAINT fk_rails_fc90d8e664 FOREIGN KEY (brokerage_profile_id) REFERENCES public.brokerage_profiles(id) ON DELETE CASCADE;


--
-- Name: request_for_proposals fk_rails_fead8f6738; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.request_for_proposals
    ADD CONSTRAINT fk_rails_fead8f6738 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: personas fk_rails_ff69b46a04; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.personas
    ADD CONSTRAINT fk_rails_ff69b46a04 FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

\unrestrict unJ3wbqUpGLOTnIGkUQcMFggVg233fwDWjh7SxZgp60QrYRnSc5ZhrddOZYUPsC

SET search_path TO "$user", public;

INSERT INTO "schema_migrations" (version) VALUES
('20250902145405'),
('20250827173138'),
('20250826145240'),
('20250813171405'),
('20250813160356'),
('20250813160259'),
('20250809211612'),
('20250808190319'),
('20250808164030'),
('20250808163935'),
('20250808162906'),
('20250807041710'),
('20250807040839'),
('20250801160411'),
('20250728163909'),
('20250723153505'),
('20250722202632'),
('20250722155154'),
('20250716172101'),
('20250715214436'),
('20250713172913'),
('20250713164301'),
('20250713164113'),
('20250713162711'),
('20250713162511'),
('20250713162011'),
('20250713153549'),
('20250704144400');

