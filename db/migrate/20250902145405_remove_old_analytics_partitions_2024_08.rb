class RemoveOldAnalyticsPartitions202408 < ActiveRecord::Migration[8.0]
  def up
    safety_assured do
      execute <<~SQL
        ALTER TABLE analytics_events DETACH PARTITION analytics_events_2024_08;
        ALTER TABLE analytics_shipper_events DETACH PARTITION analytics_shipper_events_2024_08;
        ALTER TABLE analytics_visits DETACH PARTITION analytics_visits_2024_08;

        DROP TABLE analytics_events_2024_08;
        DROP TABLE analytics_shipper_events_2024_08;
        DROP TABLE analytics_visits_2024_08;
      SQL
    end
  end

  def down
    safety_assured do
      Analytics::PartitionByMonth.premake(table: 'analytics_events', from: Date.new(2024, 8, 1), months: 1)
      Analytics::PartitionByMonth.premake(table: 'analytics_shipper_events', from: Date.new(2024, 8, 1), months: 1)
      Analytics::PartitionByMonth.premake(table: 'analytics_visits', from: Date.new(2024, 8, 1), months: 1)
    end
  end
end
