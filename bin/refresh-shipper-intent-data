#!/bin/bash
set -e

# Usage: ./bin/refresh-shipper-intent-data [number_of_months]
# Example: ./bin/refresh-shipper-intent-data 3  # includes current month and 2 previous months

green=$(tput setaf 2)
reset=$(tput sgr0)
environment=production
dbname=carrier_source_development

# Number of months to include (default: 1 for current month only)
months_to_include=${1:-1}

psql_command() {
  psql -q -h localhost -U postgres -d "${dbname}" -t -c "$1"
}

export PGOPTIONS='-c statement_timeout=0'
export PGPASSWORD=postgres

partitioned_tables=("analytics_visits" "analytics_events" "analytics_shipper_events")

# Generate list of months to process
declare -a months_to_process=()
for ((i=0; i<months_to_include; i++)); do
  if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS (BSD date)
    month_date=$(date -v-${i}m +%Y_%m)
  else
    # Linux (GNU date)
    month_date=$(date -d "$i months ago" +%Y_%m)
  fi
  months_to_process+=("$month_date")
done

echo "Fetching ${green}${environment}${reset} database url from AWS Secrets Manager..."
dburl=$(aws secretsmanager get-secret-value --secret-id carrier_source/"$environment"/postgres/carrier_source_user --query SecretString --output text | jq -r '"postgres://" + .username + ":" + .password + "@" + .host + ":" + (.port|tostring) + "/postgres"')

declare -a final_tables=()
for month in "${months_to_process[@]}"; do
  for table in "${partitioned_tables[@]}"; do
    final_tables+=("${table}_${month}")
  done
done

final_tables+=("analytics_companies")

echo "Refreshing shipper intent data for ${green}${months_to_include}${reset} month(s): ${green}${months_to_process[*]}${reset}..."
for table in "${final_tables[@]}"; do
  echo "Refreshing ${green}${table}${reset}..."
  psql_command "TRUNCATE TABLE ${table} CASCADE;"
  pg_dump "${dburl}" --format=c -f "${table}.dump" -t "${table}" --data-only --verbose --no-owner --no-acl
  pg_restore --verbose --jobs=2 -h localhost -U postgres -d "${dbname}" --no-owner --no-acl "${table}.dump"
done

echo "Cleaning up dump files..."
for table in "${final_tables[@]}"; do
  dump_file="${table}.dump"
  if [ -f "$dump_file" ]; then
    echo "Removing ${green}${dump_file}${reset}..."
    rm "$dump_file"
  else
    echo "Dump file ${green}${dump_file}${reset} not found, skipping removal."
  fi
done

echo "Indexing shipper intent data in elasticsearch..."

existing_streams=$(curl -s -X GET "http://localhost:9200/_data_stream/_all" | jq -r '.data_streams[].name')
for stream in $existing_streams; do
  if [[ $stream == analytics_shipper_events_* ]]; then
    echo "Deleting existing data stream ${green}${stream}${reset}..."
    curl -X DELETE "http://localhost:9200/_data_stream/${stream}"
  fi
done

index_name=analytics_shipper_events_development_"$(date +%Y%m%d%H%M)"

# Load analytics data for each month
for month in "${months_to_process[@]}"; do
  # Convert YYYY_MM format to YYYY-MM for time range
  year_month=$(echo "$month" | sed 's/_/-/')
  echo "Loading analytics data for ${green}${year_month}${reset}..."
  bundle exec thor elasticsearch:load_analytics --time-range "${year_month}" --index "${index_name}"
done

# Create an alias for the new index
curl -X POST "http://localhost:9200/_aliases" -H 'Content-Type: application/json' -d '{
  "actions": [
    {
      "add": {
        "index": "'"${index_name}"'",
        "alias": "analytics_shipper_events_development",
        "is_write_index": true
      }
    }
  ]
}'
