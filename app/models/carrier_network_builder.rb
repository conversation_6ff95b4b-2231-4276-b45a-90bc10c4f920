# == Schema Information
#
# Table name: carrier_network_builders
#
#  id         :bigint           not null, primary key
#  name       :string
#  uuid       :uuid             not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint
#
# Indexes
#
#  index_carrier_network_builders_on_user_id  (user_id)
#  index_carrier_network_builders_on_uuid     (uuid) UNIQUE
#
class CarrierNetworkBuilder < ApplicationRecord
  belongs_to :user, optional: true
  has_many :lanes, class_name: 'CarrierNetworkBuilderLane', dependent: :destroy
  has_many :messages, class_name: 'CarrierNetworkBuilderMessage', dependent: :destroy

  validates :name, presence: true

  before_create do
    self.uuid ||= SecureRandom.uuid
  end
end
