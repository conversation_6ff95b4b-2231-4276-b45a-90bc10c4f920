class CarrierNetworkBuildersController < ApplicationController
  before_action :require_login
  before_action :set_carrier_network_builder, only: %i(show edit update destroy chat export)

  def show
    @carrier_network_builders = CarrierNetworkBuilder.where(user: current_user)
    @lanes = @carrier_network_builder.lanes.order(:created_at)
    render layout: 'core'
  end

  def new
    @carrier_network_builders = CarrierNetworkBuilder.where(user: current_user)
    @carrier_network_builder = CarrierNetworkBuilder.new(user: current_user)
    authorize @carrier_network_builder
    render layout: 'core'
  end

  def edit
  end

  def create
    carrier_network_builder = CarrierNetworkBuilder.create(name: 'New Chat', user: current_user)
    authorize carrier_network_builder

    message = CarrierNetworkBuilders::ChatRequestHandlers::HandleRequest.call(
      builder: carrier_network_builder, params:
    )

    if message.blank?
      carrier_network_builder.destroy
      redirect_to new_carrier_network_builder_url
    else
      @result = CarrierNetworkBuilders::ShipperProspectingChatbot.call(builder: carrier_network_builder, message:)
      redirect_to carrier_network_builder_url(carrier_network_builder.uuid)
    end
  end

  def update
    if @carrier_network_builder.update(carrier_network_builder_params)
      respond_to do |format|
        format.html { redirect_to carrier_network_builder_url(@carrier_network_builder.uuid) }
        format.turbo_stream
      end
    else
      render :edit, status: :unprocessable_content
    end
  end

  def destroy
    @carrier_network_builder.destroy!
    redirect_to new_carrier_network_builder_url
  end

  def chat
    message = CarrierNetworkBuilders::ChatRequestHandlers::HandleRequest.call(
      builder: @carrier_network_builder, params:
    )

    if message.blank?
      head :no_content
    else
      @result = CarrierNetworkBuilders::ShipperProspectingChatbot.call(builder: @carrier_network_builder, message:)

      respond_to do |format|
        format.turbo_stream
      end
    end
  end

  def export
    generator = CarrierNetworkBuilders::ExportGenerators.for(params.fetch(:type, 'csv')).new(@carrier_network_builder)
    send_data generator.string, filename: generator.filename, type: generator.mime_type, disposition: 'attachment'
  end

  private

  def set_carrier_network_builder
    @carrier_network_builder = CarrierNetworkBuilder.where(user: current_user).find_by!(uuid: params[:id])
    authorize @carrier_network_builder
  end

  def carrier_network_builder_params
    params.expect(carrier_network_builder: [:name])
  end
end
