<div class="inline-flex flex-wrap text-sm gap-2">
  <% if count.positive? %>
    <%= render StarsComponent.new(rating:, class: 'fill-secondary') %>
  <% end %>
  <div class="flex gap-2">
    <span>
      <% if link %>
        <%= link_to link, class: 'text-primary hover:underline' do %>
          (<span class="font-semibold"><%= count %></span> <%= 'review'.pluralize(count) %>)
        <% end %>
      <% end %>
    </span>
    <% if count.positive? %>
      <span>
        <strong class="ml-1"><%= rating.round(1) %></strong> out of 5
      </span>
    <% end %>
  </div>
</div>
