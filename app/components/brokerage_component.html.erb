<% if header? %>
  <%= header %>
<% end %>

<% rounding = header? ? 'md:rounded-b-sm' : 'md:rounded-sm' %>

<div id="<%= dom_id(brokerage) %>"
     class="border-gray-300 border-t md:border <%= rounding %> p-6 pb-3.5 md:mb-6 relative"
     data-controller="intersection track-event" data-track-event-name-value="Brokerage Listing Viewed"
     data-track-event-properties-value="<%= { id: brokerage.id, entity_type: 'broker' }.to_json %>"
     data-action="intersection:appear->track-event#track:once">
  <div class="flex flex-row">
    <div class="flex-auto">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-auto">
          <div class="flex flex-col sm:flex-row gap-6">
            <% if logo.present? %>
              <div class="flex-none flex items-center justify-center border border-gray-300 p-1 w-[92px] h-[92px]
                          rounded-xs">
                <%= render PictureComponent.new(logo, dimensions: [82, 82], alt: "#{name} logo",
                                                      class: 'w-full h-full object-contain') %>
              </div>
            <% end %>
            <div>
              <div class="text-2xl mb-1 text-black font-semibold">
                <div class="flex flex-wrap items-center gap-2 mb-2">
                  <%= link_to brokerage_url(brokerage) do %>
                    <h2><%= name %></h2>
                  <% end %>
                  <% if claimed? %>
                    <%= helpers.svg_tag 'badge-check-solid', class: 'inline w-5 h-5 fill-primary',
                                        data: { controller: 'tippy',
                                                tippy_content: 'This brokerage profile has been claimed' } %>
                  <% else %>
                    <%= render Brokerages::UnclaimedTagComponent.new(brokerage) %>
                  <% end %>
                  <ul class="flex gap-2 text-sm text-primary-900 font-normal">
                    <% authority_numbers.each_with_index do |number, index| %>
                      <li class="inline-block"><%= number %></li>
                      <% if index < authority_numbers.size - 1 %>
                        <li class="inline-block">&#124;</li>
                      <% end %>
                    <% end %>
                  </ul>
                </div>
              </div>
              <%= render StarsReviewsComponent.new(rating: star_rating, count: review_count,
                                                   link: brokerage_url(brokerage, anchor: 'reviews')) %>
              <address class="not-italic mt-1">
                <span><%= city.name %></span>,
                <span><%= city.state_code %></span>
                <span><%= city.country_code %></span>
                <span><%= zip %></span>
              </address>
            </div>
          </div>
        </div>
        <% if aside? %>
          <%= aside %>
        <% end %>
      </div>
    </div>
  </div>

  <div class="-mx-6 my-4 border border-gray-300 border-x-0 flex items-stretch">
    <div class="flex py-4 px-2 border-r border-r-gray-300 bg-purple-50 items-center">
      <%= helpers.svg_tag 'circle-nodes-regular', class: 'w-4 h-4 fill-purple',
                          data: { controller: 'tippy', tippy_content: 'This company is a broker' } %>
    </div>
    <dl class="py-3 px-6">
      <dt class="inline pr-1 after:content-[':']">Authority</dt>
      <dd class="inline">
        <span class="rounded-full h-2.5 w-2.5 inline-block border-2 <%= authority.css %>"></span>
        <span class="x-value-authority"><%= authority.title %></span>
        <%= authority.date ? "(#{l(authority.date, format: :mdy)})" : nil %>
      </dd>
    </dl>
  </div>

  <% if freights.present? || shipment_types.present? %>
    <div class="flex flex-col md:flex-row mt-2">
      <% if freights.present? %>
        <div class="w-full md:w-1/2" data-controller="read-more" data-read-more-more-text-value="See all"
             data-read-more-less-text-value="See fewer">
          <h3 class="font-semibold text-black">
            <%= t('helpers.label.carrier.freight') %>
            <span class="text-gray-600 font-normal x-value-freights-size">(<%= freights.size %>)</span>
          </h3>
          <ul data-read-more-target="content" class="list-disc ml-4 mt-0.5 flex-wrap flex mb-[-4px] text-sm">
            <% freights[0...4].each do |freight| %>
              <li class="w-1/2 pr-2 mb-1"><%= freight.name %></li>
            <% end %>
          </ul>
          <template data-read-more-target="full">
            <% freights.each do |freight| %>
              <li class="w-1/2 pr-2 mb-1"><%= freight.name %></li>
            <% end %>
          </template>
          <% if freights.size > 4 %>
            <a class="block text-primary ml-4 mt-1 hover:underline" href="javascript:void(0)"
               data-action="read-more#toggle">See all</a>
          <% end %>
        </div>
      <% end %>

      <% if shipment_types.present? %>
        <div class="w-full md:w-1/2 mt-4 md:mt-0">
          <h3 class="font-semibold text-black">
            <%= t('helpers.label.carrier.shipment_types') %>
            <span class="text-gray-600 font-normal">(<%= shipment_types.size %>)</span>
          </h3>
          <ul class="list-disc ml-4 mt-0.5 flex-wrap flex mb-[-4px] text-sm">
            <% shipment_types.each do |shipment_type| %>
              <li class="w-1/2 pr-2 mb-1"><%= shipment_type.name %></li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>

    <%= render HorizontalRuleComponent.new %>
  <% end %>

  <% if footer? %>
    <%= footer %>
  <% end %>
</div>
