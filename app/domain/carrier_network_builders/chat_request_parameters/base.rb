module CarrierNetworkBuilders
  module ChatRequestParameters
    class Base
      attr_reader :message

      delegate :carrier_network_builder, :file, to: :message

      def initialize(message:)
        @message = message
      end

      def match?
        true
      end

      def model
        'gpt-4o'
      end

      def temperature
        0.7
      end

      def max_tokens
        1000
      end

      def messages
        [{ role: message.role, content: message.content }]
      end

      def previous_messages
        carrier_network_builder.messages.where.not(id: message.id).order(:created_at)
          .map { |m| { role: m.role, content: m.content } }
      end
    end
  end
end
