module CarrierNetworkBuilders
  module ChatRequestParameters
    class ImageFileUpload < Base
      def match?
        file.present? && file.content_type.start_with?('image/')
      end

      def temperature
        0.2
      end

      def max_tokens
        6000
      end

      def messages
        [
          {
            role: 'user',
            content: [
              { type: 'text', text: 'Analyze the image I am uploading to extract shipping lanes.' },
              {
                type: 'image_url',
                image_url: { url: "data:image/jpeg;base64,#{Base64.strict_encode64(file.download)}" }
              }
            ]
          }
        ]
      end
    end
  end
end
