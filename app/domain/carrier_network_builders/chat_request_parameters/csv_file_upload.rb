module CarrierNetworkBuilders
  module ChatRequestParameters
    class CsvFileUpload < Base
      def match?
        file.present? && file.content_type == 'text/csv'
      end

      def temperature
        0.2
      end

      def max_tokens
        (content.length / 4).clamp(8000, 12_000)
      end

      def messages
        [
          {
            role: 'user',
            content: "Analyze the following CSV file to extract shipping lanes.\n\n#{content}"
          }
        ]
      end

      def content
        @content ||= file.download
      end
    end
  end
end
