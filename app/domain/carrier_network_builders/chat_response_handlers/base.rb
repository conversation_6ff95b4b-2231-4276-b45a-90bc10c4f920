module CarrierNetworkBuilders
  module ChatResponseHandlers
    class Base
      attr_reader :builder, :response

      def initialize(builder:, response:)
        @builder = builder
        @response = response
      end

      def handle?
        true
      end

      def content
        @content ||= response.parse.dig('choices', 0, 'message', 'content')
      end

      def message
        { role: 'assistant', content: chat_response.message }
      end

      def lanes
        []
      end

      def chat_response
        @chat_response ||= CarrierNetworkBuilders::ChatResponse.new(JSON.parse(content))
      rescue JSON::ParserError
        CarrierNetworkBuilders::ChatResponse.new(message: content)
      end
    end
  end
end
