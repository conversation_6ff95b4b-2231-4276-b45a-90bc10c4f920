module CarrierNetworkBuilders
  module ChatRequestParameters
    HANDLERS = %w(ImageFileUpload CsvFileUpload Base).freeze

    def self.for(message:)
      handler = HANDLERS.lazy.map { |name| const_get(name, false).new(message:) }.find(&:match?)

      messages = [
        { role: 'system', content: CarrierNetworkBuilders::ChatRequestParameters::Completion.prompt },
        *handler.previous_messages,
        *handler.messages
      ]

      {
        messages:, model: handler.model, temperature: handler.temperature, max_completion_tokens: handler.max_tokens,
        response_format: {
          type: 'json_schema',
          json_schema: CarrierNetworkBuilders::ChatRequestParameters::Completion.json_schema
        }
      }
    end
  end
end
