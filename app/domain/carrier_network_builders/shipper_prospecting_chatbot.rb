module CarrierNetworkBuilders
  class ShipperProspectingChatbot
    include Callable

    attr_reader :builder, :message

    def initialize(builder:, message:)
      @builder = builder
      @message = message
    end

    def call
      parameters = ChatRequestParameters.for(message:)
      response = OpenAI::Api::ChatCompletion.new(raise_errors: true).create(**parameters)
      ChatResponseHandlers::HandleResponse.call(builder:, response:)
    end
  end
end
