module Analytics
  class MatchProspectiveCompanies
    include Callable

    BUSINESS_SUFFIXES = /\b(inc|llc|ltd|co|corp|company|incorporated|limited)\b\.?/i

    attr_reader :bucket, :input, :output, :field_mappings

    # :nocov:
    def initialize(bucket:, input:, output:, field_mappings:)
      @bucket = bucket
      @input = input
      @output = output
      @field_mappings = field_mappings
    end

    # rubocop:disable Metrics
    def call
      CSV.open('tmp/matched-companies.csv', 'wb') do |data|
        data << [*csv.headers, 'Matched Fields', 'Search Activity']

        csv.each do |row|
          attrs = extract_company_data(row)
          query = build_company_match_query(name: attrs[:name], domain: attrs[:domain], phone: attrs[:phone])
          response = Analytics::Company.es.search(query).response

          if response['hits']['hits'].any?
            hit = response['hits']['hits'].first
            highlight = hit['highlight'] || {}
            search_activity = Analytics::ShipperEvent.exists?(
              analytics_company_id: hit['_id'], type: 'carrier.search.performed'
            )
            data << [*row.fields, highlight.keys.join(', '), search_activity]
          else
            data << [*row.fields, '', false]
          end
        end
      end

      Aws::S3::Object.new(bucket, output).upload_file('tmp/matched-companies.csv')
    end
    # rubocop:enable Metrics

    private

    def csv
      @csv ||= Aws::S3::Object.new(bucket, input).then do |object|
        CSV.parse(object.get.body, headers: true)
      end
    end

    def clean_name_for_search(name)
      name.gsub(BUSINESS_SUFFIXES, '').strip
    end

    def build_company_match_query(name:, domain: nil, phone: nil)
      dis_max_queries = [{ match: { name: { query: clean_name_for_search(name), operator: 'and', boost: 1.0 } } }]
      dis_max_queries << { term: { domain: { value: domain.strip, boost: 3.0 } } } if domain.to_s.strip.present?
      dis_max_queries << { term: { phone: { value: phone.strip, boost: 2.0 } } } if phone.to_s.strip.present?

      {
        query: {
          bool: {
            must: { dis_max: { tie_breaker: 0.3, queries: dis_max_queries } },
            filter: { range: { updated_at: { gte: 'now-1y' } } }
          }
        },
        highlight: { fields: { name: {}, domain: {}, phone: {} }, require_field_match: false },
        size: 1,
        _source: %w(name domain phone)
      }
    end

    def extract_company_data(row)
      {
        name: row[field_mappings['name']],
        phone: Functions::FormatPhone.call(phone: row[field_mappings['phone']], country: 'US'),
        domain: extract_domain(row[field_mappings['domain']])
      }
    end

    def extract_domain(url)
      host = Addressable::URI.heuristic_parse(url).try(:host)
      host.present? ? PublicSuffix.parse(host).domain : nil
    rescue StandardError
      nil
    end
    # :nocov:
  end
end
