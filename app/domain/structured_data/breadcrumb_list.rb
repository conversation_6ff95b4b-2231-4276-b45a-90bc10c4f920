module StructuredData
  class BreadcrumbList
    attr_reader :breadcrumbs

    def initialize(breadcrumbs)
      @breadcrumbs = breadcrumbs
    end

    def as_json(...)
      {
        '@context' => 'https://schema.org',
        '@type' => 'BreadcrumbList',
        'itemListElement' => breadcrumbs.map.with_index(1) do |item, index|
          {
            '@type' => 'ListItem',
            'position' => index,
            'name' => item[:text],
            'item' => item[:href]
          }
        end
      }
    end
  end
end
