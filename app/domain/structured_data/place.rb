module StructuredData
  class Place < Base
    attr_reader :city, :name

    def initialize(city:, name:)
      @city = city
      @name = name
    end

    def as_json(...)
      {
        '@type' => 'Place',
        'name' => format(name, location: city.label),
        'geo' => {
          '@type' => 'GeoCoordinates',
          'latitude' => city.latitude,
          'longitude' => city.longitude
        },
        'address' => {
          '@type' => 'PostalAddress',
          'addressLocality' => city.name,
          'addressRegion' => city.state_code,
          'addressCountry' => city.country_code
        }
      }
    end
  end
end
