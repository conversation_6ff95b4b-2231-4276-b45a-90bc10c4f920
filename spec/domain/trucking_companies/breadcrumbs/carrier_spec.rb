require 'rails_helper'

RSpec.describe TruckingCompanies::Breadcrumbs::Carrier do
  subject(:breadcrumb) { described_class.new(carrier) }

  let(:city) { create :city, name: 'Or<PERSON>', state_code: 'UT' }
  let(:company) { create :company, :carrier, legal_name: 'Putnik Express', city: }
  let(:carrier) { company.as_entity(:carrier) }

  describe '#items' do
    it 'returns all ancestor items including self' do
      expect(breadcrumb.items).to(
        eq(
          [
            { href: 'http://www.lvh.me:3001/trucking-companies', text: 'Trucking Companies' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states', text: 'United States' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states/utah', text: 'Utah' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states/utah/orem', text: 'Orem' },
            { href: 'http://www.lvh.me:3001/carriers/putnik-express', text: 'Putnik Express' }
          ]
        )
      )
    end
  end
end
