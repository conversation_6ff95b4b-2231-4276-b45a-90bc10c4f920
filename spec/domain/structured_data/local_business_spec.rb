require 'rails_helper'

RSpec.describe StructuredData::LocalBusiness do
  subject(:local_business) { described_class.new(carrier) }

  let(:company) { create :company, :carrier }
  let(:carrier) { company.as_entity(:carrier) }

  describe '#as_json' do
    context 'when the carrier has a logo' do
      let!(:carrier_profile) { create :carrier_profile, :with_assets, company: }

      it 'returns structured data with logo' do
        expect(local_business.as_json).to match hash_including(
          '@type' => 'LocalBusiness',
          'name' => carrier.name,
          'logo' => an_instance_of(String)
        )
      end
    end

    context 'when the carrier does not have a logo' do
      let!(:carrier_profile) { create :carrier_profile, company: }

      it 'returns structured data without logo' do
        expect(local_business.as_json).to match hash_including(
          '@type' => 'LocalBusiness',
          'name' => carrier.name
        )
      end
    end

    context 'when the carrier has reviews' do
      before do
        ReviewsAggregate.create(review_count: 10, star_rating: 4.5, company:)
      end

      it 'returns structured data with aggregate rating' do
        expect(local_business.as_json).to match hash_including(
          '@type' => 'LocalBusiness',
          'aggregateRating' => {
            '@type' => 'AggregateRating',
            'ratingValue' => 4.5,
            'reviewCount' => 10,
            'bestRating' => '5',
            'worstRating' => '0'
          }
        )
      end
    end
  end
end
