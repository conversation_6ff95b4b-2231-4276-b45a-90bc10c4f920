require 'rails_helper'

RSpec.describe CarrierNetworkBuilders::ChatResponseHandlers::LaneCreation do
  describe '#lanes' do
    let(:builder) { create :carrier_network_builder }
    let(:response) { instance_double(HTTP::Response, parse: parsed_response) }
    let(:handler) { described_class.new(builder: builder, response: response) }

    context 'when content contains valid JSON block' do
      let(:content) do
        {
          'message' => 'Here are the lanes you requested:',
          'data' => {
            'lanes' => [
              { 'origin_id' => 'united-states:utah', 'destination_id' => 'united-states:nevada' }
            ]
          }
        }
      end

      let(:parsed_response) do
        {
          'choices' => [
            {
              'message' => {
                'content' => content.to_json
              }
            }
          ]
        }
      end

      it 'returns formatted lanes' do
        expect(handler.lanes).to(
          contain_exactly(origin_id: 'united-states:utah', destination_id: 'united-states:nevada', filters: {})
        )
      end
    end

    context 'when JSON parsing fails' do
      let(:parsed_response) do
        {
          'choices' => [
            {
              'message' => {
                'content' => 'Invalid JSON content'
              }
            }
          ]
        }
      end

      it 'returns an empty array and does not raise an error' do
        expect(handler.lanes).to eq([])
      end
    end
  end
end
