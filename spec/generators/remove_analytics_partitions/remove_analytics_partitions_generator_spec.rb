require 'rails_helper'
require 'generators/remove_analytics_partitions/remove_analytics_partitions_generator'

RSpec.describe RemoveAnalyticsPartitionsGenerator, type: :generator do
  describe 'migration file generation' do
    it 'creates a migration file for the specified partition date' do
      run_generator ['2022-12-01']

      migration_file = Dir.glob(File.join(destination_root,
                                          'db/migrate/*remove_old_analytics_partitions_2022_12.rb')).first
      expect(migration_file).not_to be_nil

      content = File.read(migration_file)
      expect(content).to include('class RemoveOldAnalyticsPartitions202212 < ActiveRecord::Migration[8.0]')
      expect(content).to include('DROP TABLE analytics_events_2022_12;')
    end

    context 'with invalid date format' do
      it 'raises an error' do
        expect { run_generator ['invalid-date'] }.to raise_error(ArgumentError)
      end
    end
  end
end
